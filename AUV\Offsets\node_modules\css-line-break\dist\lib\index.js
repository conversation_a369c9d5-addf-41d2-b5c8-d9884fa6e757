"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LineBreaker = exports.fromCodePoint = exports.toCodePoints = void 0;
var Util_1 = require("./Util");
Object.defineProperty(exports, "toCodePoints", { enumerable: true, get: function () { return Util_1.toCodePoints; } });
Object.defineProperty(exports, "fromCodePoint", { enumerable: true, get: function () { return Util_1.fromCodePoint; } });
var LineBreak_1 = require("./LineBreak");
Object.defineProperty(exports, "LineBreaker", { enumerable: true, get: function () { return LineBreak_1.LineBreaker; } });
//# sourceMappingURL=index.js.map