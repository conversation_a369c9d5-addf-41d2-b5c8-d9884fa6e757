import React from 'react'

const inputStyle = {
  width: '100%',
  padding: '0.5rem',
  background: '#444',
  border: '1px solid #666',
  borderRadius: '4px',
  color: 'white',
  fontSize: '0.9rem'
}

const labelStyle = {
  display: 'block',
  marginBottom: '0.3rem',
  fontSize: '0.8rem',
  fontWeight: 'bold',
  color: '#cc5500'
}

// Mission Profile Tab
export function MissionTab({ config, updateConfig }) {
  const handleChange = (field, value) => {
    updateConfig('profile', { [field]: value })
  }

  return (
    <div>
      <h3 style={{ color: '#cc5500', marginBottom: '1rem' }}>🎯 Mission Profile</h3>
      
      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Mission Details</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Mission Type:</label>
          <select
            value={config.type}
            onChange={(e) => handleChange('type', e.target.value)}
            style={inputStyle}
          >
            <option value="Survey">Survey</option>
            <option value="Inspection">Inspection</option>
            <option value="Search">Search & Recovery</option>
            <option value="Mapping">Mapping</option>
            <option value="Environmental">Environmental Monitoring</option>
            <option value="Research">Research</option>
          </select>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Survey Pattern:</label>
          <select
            value={config.pattern}
            onChange={(e) => handleChange('pattern', e.target.value)}
            style={inputStyle}
          >
            <option value="Lawn Mower">Lawn Mower</option>
            <option value="Spiral">Spiral</option>
            <option value="Grid">Grid</option>
            <option value="Perimeter">Perimeter</option>
            <option value="Waypoint">Waypoint Navigation</option>
            <option value="Custom">Custom Pattern</option>
          </select>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Survey Area:</label>
          <input
            type="text"
            value={config.area}
            onChange={(e) => handleChange('area', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 1km²"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Estimated Duration:</label>
          <input
            type="text"
            value={config.duration}
            onChange={(e) => handleChange('duration', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 8 hours"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Priority:</label>
          <select
            value={config.priority}
            onChange={(e) => handleChange('priority', e.target.value)}
            style={inputStyle}
          >
            <option value="Low">Low</option>
            <option value="Medium">Medium</option>
            <option value="High">High</option>
            <option value="Critical">Critical</option>
          </select>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Classification:</label>
          <select
            value={config.classification}
            onChange={(e) => handleChange('classification', e.target.value)}
            style={inputStyle}
          >
            <option value="Unclassified">Unclassified</option>
            <option value="Restricted">Restricted</option>
            <option value="Confidential">Confidential</option>
            <option value="Secret">Secret</option>
          </select>
        </div>
      </div>
    </div>
  )
}

// Safety Parameters Tab
export function SafetyTab({ config, updateConfig }) {
  const handleChange = (section, field, value) => {
    if (section) {
      updateConfig('safety', {
        ...config,
        [section]: {
          ...config[section],
          [field]: value
        }
      })
    } else {
      updateConfig('safety', { [field]: value })
    }
  }

  return (
    <div>
      <h3 style={{ color: '#cc5500', marginBottom: '1rem' }}>🛡️ Safety Parameters</h3>
      
      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Emergency Limits</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Abort Depth:</label>
          <input
            type="text"
            value={config.abortDepth}
            onChange={(e) => handleChange(null, 'abortDepth', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 3100m"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Safety Margin:</label>
          <input
            type="text"
            value={config.safetyMargin}
            onChange={(e) => handleChange(null, 'safetyMargin', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 10%"
          />
        </div>
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Return Triggers</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Low Battery Threshold:</label>
          <input
            type="text"
            value={config.returnTriggers.lowBattery}
            onChange={(e) => handleChange('returnTriggers', 'lowBattery', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 20%"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Lost Communications Timeout:</label>
          <input
            type="text"
            value={config.returnTriggers.lostComms}
            onChange={(e) => handleChange('returnTriggers', 'lostComms', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 30 minutes"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Sensor Failure Response:</label>
          <select
            value={config.returnTriggers.sensorFailure}
            onChange={(e) => handleChange('returnTriggers', 'sensorFailure', e.target.value)}
            style={inputStyle}
          >
            <option value="Continue">Continue Mission</option>
            <option value="Critical Only">Critical Sensors Only</option>
            <option value="Any Failure">Any Sensor Failure</option>
            <option value="Immediate Return">Immediate Return</option>
          </select>
        </div>
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Emergency Procedures</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>
            <input
              type="checkbox"
              checked={config.emergencyProcedures.surfaceImmediate}
              onChange={(e) => handleChange('emergencyProcedures', 'surfaceImmediate', e.target.checked)}
              style={{ marginRight: '0.5rem' }}
            />
            Surface Immediately on Emergency
          </label>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>
            <input
              type="checkbox"
              checked={config.emergencyProcedures.activateBeacon}
              onChange={(e) => handleChange('emergencyProcedures', 'activateBeacon', e.target.checked)}
              style={{ marginRight: '0.5rem' }}
            />
            Activate Emergency Beacon
          </label>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>
            <input
              type="checkbox"
              checked={config.emergencyProcedures.dataUpload}
              onChange={(e) => handleChange('emergencyProcedures', 'dataUpload', e.target.checked)}
              style={{ marginRight: '0.5rem' }}
            />
            Upload Critical Data
          </label>
        </div>
      </div>
    </div>
  )
}

// Data Collection Tab
export function DataTab({ config, updateConfig }) {
  const handleChange = (field, value) => {
    updateConfig('dataCollection', { [field]: value })
  }

  return (
    <div>
      <h3 style={{ color: '#cc5500', marginBottom: '1rem' }}>💾 Data Collection</h3>
      
      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Logging Configuration</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Logging Interval:</label>
          <select
            value={config.loggingInterval}
            onChange={(e) => handleChange('loggingInterval', e.target.value)}
            style={inputStyle}
          >
            <option value="0.1 second">0.1 second</option>
            <option value="0.5 second">0.5 second</option>
            <option value="1 second">1 second</option>
            <option value="5 seconds">5 seconds</option>
            <option value="10 seconds">10 seconds</option>
          </select>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>File Format:</label>
          <select
            value={config.fileFormat}
            onChange={(e) => handleChange('fileFormat', e.target.value)}
            style={inputStyle}
          >
            <option value="XTF">XTF (Extended Triton Format)</option>
            <option value="GSF">GSF (Generic Sensor Format)</option>
            <option value="S7K">S7K (Reson 7K)</option>
            <option value="ALL">ALL (Kongsberg)</option>
            <option value="Custom">Custom Format</option>
          </select>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Storage Capacity:</label>
          <input
            type="text"
            value={config.storageCapacity}
            onChange={(e) => handleChange('storageCapacity', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 2TB"
          />
        </div>
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Data Management</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Compression:</label>
          <select
            value={config.compression}
            onChange={(e) => handleChange('compression', e.target.value)}
            style={inputStyle}
          >
            <option value="Disabled">Disabled</option>
            <option value="Enabled">Enabled</option>
            <option value="Lossless">Lossless Only</option>
            <option value="Adaptive">Adaptive</option>
          </select>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Backup Strategy:</label>
          <select
            value={config.backup}
            onChange={(e) => handleChange('backup', e.target.value)}
            style={inputStyle}
          >
            <option value="None">No Backup</option>
            <option value="Redundant">Redundant Storage</option>
            <option value="Cloud">Cloud Backup</option>
            <option value="Both">Redundant + Cloud</option>
          </select>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Quality Control:</label>
          <select
            value={config.qualityControl}
            onChange={(e) => handleChange('qualityControl', e.target.value)}
            style={inputStyle}
          >
            <option value="Disabled">Disabled</option>
            <option value="Real-time">Real-time</option>
            <option value="Post-processing">Post-processing</option>
            <option value="Both">Real-time + Post</option>
          </select>
        </div>
      </div>
    </div>
  )
}
