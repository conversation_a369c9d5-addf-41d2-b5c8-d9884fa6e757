# jsx-a11y/scope

💼 This rule is enabled in the following configs: ☑️ `recommended`, 🔒 `strict`.

<!-- end auto-generated rule header -->

The `scope` prop should be used only on `<th>` elements.

## Rule details

This rule takes no arguments.

### Succeed
```jsx
<th scope="col" />
<th scope={scope} />
```

### Fail

```jsx
<div scope />
```

## Accessibility guidelines
- [WCAG 1.3.1](https://www.w3.org/WAI/WCAG21/Understanding/info-and-relationships)
- [WCAG 4.1.1](https://www.w3.org/WAI/WCAG21/Understanding/parsing)

### Resources
- [axe-core, scope-attr-valid](https://dequeuniversity.com/rules/axe/3.5/scope-attr-valid)
