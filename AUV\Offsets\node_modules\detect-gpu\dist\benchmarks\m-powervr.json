["5", ["powervr rogue g6110", "6110", "g6110 powervr rogue", 0, [[1024, 600, 11, "dasaita mtcd px5 head unit"], [1280, 752, 8, "visual land prestige prime 10se"], [1366, 720, 7, "ditecma m1092r"], [1920, 1008, 6, "vensmile t051 tv box"], [1920, 1016, 5, "geekbuying geekbox tv box"], [1920, 1032, 4, "hannspree hsg1351"], [1920, 1080, 5, "10moons tv box (rogue g6110)"], [1920, 1128, 5, "teclast p10"]]], ["powervr rogue g6200", "6200", "g6200 powervr rogue", 0, [[1280, 720, 15, "infocus m530"], [1280, 736, 12, "amazon kindle fire hd 8 (5th gen, kfmewi)"], [1280, 752, 12, "amazon kindle fire hd 10 (5th gen, kftbwi)"], [1280, 800, 9, "amazon kindle fire hd 7 (4th gen, kfaswi)"], [1794, 1080, 5, "ubik uno"], [1920, 1080, 6, "cherry mobile x220 cosmos one plus"], [1920, 1152, 8, "meizu mx4 (m460, m460a, m461)"], [2392, 1440, 6, "hasee hl9916004"], [2560, 1440, 6, "condor allure a100 pgn-607"]]], ["powervr rogue g6230", "6230", "g6230 powervr rogue", 0, [[1920, 1008, 8, "rikomagic mk80 tv box (tronsmart draco aw80, fantasy a80)"], [1920, 1016, 7, "cubietech cubieboard 4 (cc-a80, hansen-a80, development board)"], [2048, 1440, 5, "teclast p98air"], [2048, 1464, 7, "actions gs900a (development board)"]]], ["powervr rogue g6400", "6400", "g6400 powervr rogue", 0, [[1794, 1080, 8, "lg f490 liger (g6400)"], [1920, 1032, 8, "renesas lager"]]], ["powervr rogue g6430", "6430", "g6430 powervr rogue", 0, [[1024, 552, 26, "asus fonepad 7 (k01f fe171mg)"], [1280, 720, 27, "asus zenfone 2 (z008 ze550ml)"], [1280, 736, 20, "asus fonepad 7 (k019 fe375cg)"], [1280, 752, 27, "asus zenpad 10 (p01t z300cl)"], [1788, 1080, 8, "lg f490 liger (g6430)"], [1920, 1080, 15, "asus zenfone zoom (intel z3560, z00xsb zx551ml)"], [1920, 1104, 11, "asus memo pad 8 ast21 (intel z3580, k015 me581cl)"], [2048, 1440, 11, "asus zenpad s 8.0 (p01m z580c)"], [2560, 1504, 9, "dell venue 10 7040"]]], ["powervr rogue ge8100", "8100", "ge8100 powervr rogue", 0, [[906, 480, 8, "tinno k600"], [1184, 720, 5, "mediatek mt6739 (development board, rogue ge8100)"], [1339, 720, 5, "vodafone vfd 720"], [1344, 720, 6, "gionee f205"]]], ["powervr rogue ge8300", "8300", "ge8300 powervr rogue", 0, [[1280, 752, 9, "acer b3-a40 iconia one 10"], [1208, 800, 8, "verizon qtaki1"], [1920, 1128, 5, "acer b3-a40 fhd iconia one 10"]]], ["powervr rogue ge8320", "8320", "ge8320 powervr rogue", 0, [[1465, 720, 21, "samsung galaxy a12"]]], ["powervr rogue gx6250", "6250", "gx6250 powervr rogue", 0, [[688, 412, 16, "lenovo n23 yoga / flex 11 chromebook"], [1280, 672, 25, "renesas salvator-x-r8a7796"], [1280, 736, 25, "mediatek mt8173 (development board)"], [1920, 980, 10, "google chromebook pixel (2015, rogue gx6250)"], [1920, 1016, 14, "peloton ruby"], [1920, 1020, 8, "acer chromebook r13"], [1920, 1032, 13, "renesas salvator-x-m3"], [1920, 1080, 12, "xiaomi mibox 3 pro tv box"], [1920, 1128, 10, "amazon kindle fire hd 10 (2017, kfsuwi)"], [2048, 1536, 7, "alps jdtab j01"], [2560, 1504, 7, "onda f109"]]], ["powervr rogue gx6650", "6650", "gx6650 powervr rogue", 0, [[1280, 672, 52, "renesas salvator-x"], [1920, 968, 30, "renesas salvator-x-r8a7795"], [1920, 1032, 24, "renesas salvator-x (octa core)"]]], ["powervr rogue han", "han", "han powervr rogue", 0, [[1794, 1080, 6, "ireadygo w3d"], [1920, 1080, 6, "changhong x6"], [2392, 1440, 5, "alcatel one touch d820"], [2560, 1440, 4, "alcatel 6071y phantom"]]], ["powervr rogue hood", "", "hood powervr rogue", 0, [[1280, 736, 16, "dell venue 7 3740"], [1920, 1080, 12, "lenovo p90"], [1920, 1104, 9, "dell venue 8 3840"]]], ["powervr rogue lando", "", "lando powervr rogue", 0, [[1920, 1080, 13, "spreadtrum sp9861e (development board, rogue lando)"]]], ["powervr rogue marlowe", "", "marlowe powervr rogue", 0, [[1920, 1080, 39, "meitu v6 mp1605"], [2560, 1440, 25, "meizu pro 7 plus"]]]]