import React from 'react'
import { useSensors } from '../context/SensorContext'

export function DebugPanel() {
  const { sensors, selectedSensor, modelLoaded, showLabels } = useSensors()

  return (
    <div style={{
      position: 'fixed',
      top: '60px',
      right: '10px',
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '1rem',
      borderRadius: '4px',
      fontSize: '0.8rem',
      maxWidth: '200px',
      zIndex: 1000,
      border: '1px solid #333'
    }}>
      <h4 style={{ margin: '0 0 0.5rem 0', color: '#4CAF50' }}>Debug Info</h4>
      <div>Sensors: {sensors.length}</div>
      <div>Selected: {selectedSensor || 'None'}</div>
      <div>Model Loaded: {modelLoaded ? 'Yes' : 'No'}</div>
      <div>Labels: {showLabels ? 'ON' : 'OFF'}</div>
      <div style={{ fontSize: '0.7rem', marginTop: '0.5rem', color: '#888' }}>
        X=Starboard, Y=Forward, Z=Down
      </div>
      
      <details style={{ marginTop: '0.5rem' }}>
        <summary style={{ cursor: 'pointer', color: '#61dafb' }}>
          Sensor List
        </summary>
        <div style={{ marginTop: '0.25rem', fontSize: '0.7rem' }}>
          {sensors.map(sensor => (
            <div key={sensor.id} style={{ 
              margin: '0.25rem 0',
              padding: '0.25rem',
              background: selectedSensor === sensor.id ? '#333' : 'transparent',
              borderRadius: '2px'
            }}>
              <div style={{ color: sensor.color }}>
                {sensor.name}
              </div>
              <div style={{ color: '#ccc' }}>
                {sensor.position.map(p => p.toFixed(2)).join(', ')}
              </div>
            </div>
          ))}
        </div>
      </details>
    </div>
  )
}
