import React, { useState, useRef, useEffect } from 'react'
import { useSensors } from '../context/SensorContext'

export function DebugPanel() {
  const { sensors, selectedSensor, modelLoaded, showLabels, modelType, customModelUrl } = useSensors()
  const [position, setPosition] = useState({ x: window.innerWidth - 200, y: 80 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const panelRef = useRef()

  const handleMouseDown = (e) => {
    // Only allow dragging from the header area
    if (!e.target.closest('.debug-header')) return

    setIsDragging(true)
    const rect = panelRef.current.getBoundingClientRect()
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    })
    e.preventDefault()
  }

  const handleMouseMove = (e) => {
    if (!isDragging) return

    const newX = e.clientX - dragOffset.x
    const newY = e.clientY - dragOffset.y

    // Keep panel within viewport bounds
    const maxX = window.innerWidth - 200
    const maxY = window.innerHeight - 200

    setPosition({
      x: Math.max(0, Math.min(maxX, newX)),
      y: Math.max(0, Math.min(maxY, newY))
    })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, dragOffset])

  return (
    <div
      ref={panelRef}
      onMouseDown={handleMouseDown}
      style={{
        position: 'fixed',
        left: `${position.x}px`,
        top: `${position.y}px`,
        background: 'rgba(0,0,0,0.9)',
        color: 'white',
        padding: '0.75rem',
        borderRadius: '6px',
        fontSize: '0.75rem',
        width: '180px',
        zIndex: 1000,
        border: '1px solid #444',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
        cursor: isDragging ? 'grabbing' : 'grab',
        userSelect: 'none'
      }}>
      <div
        className="debug-header"
        style={{
          margin: '-0.75rem -0.75rem 0.5rem -0.75rem',
          padding: '0.5rem 0.75rem',
          background: 'rgba(76, 175, 80, 0.2)',
          borderRadius: '6px 6px 0 0',
          borderBottom: '1px solid #4CAF50',
          cursor: isDragging ? 'grabbing' : 'grab',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <h4 style={{ margin: 0, color: '#4CAF50', fontSize: '0.8rem' }}>Debug Info</h4>
        <div style={{
          color: '#4CAF50',
          fontSize: '0.7rem',
          opacity: 0.7
        }}>
          ⋮⋮
        </div>
      </div>
      <div>Sensors: {sensors.length}</div>
      <div>Selected: {selectedSensor || 'None'}</div>
      <div>Model: {modelType === 'fallback' ? 'Default' : modelType.toUpperCase()}</div>
      <div>Labels: {showLabels ? 'ON' : 'OFF'}</div>
      {customModelUrl && (
        <div style={{ fontSize: '0.65rem', color: '#2196F3', marginTop: '0.25rem' }}>
          Custom model loaded
        </div>
      )}
      <div style={{ fontSize: '0.7rem', marginTop: '0.5rem', color: '#888' }}>
        X=Starboard, Y=Forward, Z=Down
      </div>
      
      <details style={{ marginTop: '0.5rem' }}>
        <summary style={{ cursor: 'pointer', color: '#61dafb' }}>
          Sensor List
        </summary>
        <div style={{ marginTop: '0.25rem', fontSize: '0.7rem' }}>
          {sensors.map(sensor => (
            <div key={sensor.id} style={{ 
              margin: '0.25rem 0',
              padding: '0.25rem',
              background: selectedSensor === sensor.id ? '#333' : 'transparent',
              borderRadius: '2px'
            }}>
              <div style={{ color: sensor.color }}>
                {sensor.name}
              </div>
              <div style={{ color: '#ccc' }}>
                {sensor.position.map(p => p.toFixed(2)).join(', ')}
              </div>
            </div>
          ))}
        </div>
      </details>
    </div>
  )
}
