import React from 'react'
import { SensorStudio } from './components/SensorStudio'
import { SensorProvider } from './context/SensorContext'
import { AUVConfigProvider } from './context/AUVConfigContext'
import { ErrorBoundary } from './components/ErrorBoundary'

function App() {
  return (
    <ErrorBoundary>
      <AUVConfigProvider>
        <SensorProvider>
          <SensorStudio />
        </SensorProvider>
      </AUVConfigProvider>
    </ErrorBoundary>
  )
}

export default App
