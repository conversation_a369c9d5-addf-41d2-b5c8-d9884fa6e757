import React, { useState, Suspense, use<PERSON><PERSON>back, useMemo } from "react";
import { Canvas } from "@react-three/fiber";
import { OrbitControls, Html, useGLTF } from "@react-three/drei";
import { AxesHelper } from "three";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, References } from "./components";

function HuginModel({ scale = 1 }) {
  try {
    const gltf = useGLTF("/model.glb");
    return <primitive object={gltf.scene} scale={scale} position={[0, 0, 0]} />;
  } catch (error) {
    console.error("Failed to load 3D model:", error);
    return (
      <mesh>
        <boxGeometry args={[1, 0.2, 0.1]} />
        <meshStandardMaterial color="#666" />
      </mesh>
    );
  }
}

function Scene({ sensors, hoveredSensor, setHoveredSensor }) {
  return (
    <Canvas
      camera={{ position: [0, 1.5, 4], fov: 50 }}
      style={{ height: "500px", background: "#0b0c10" }}
    >
      <ambientLight intensity={0.5} />
      <directionalLight position={[5, 5, 5]} intensity={1.2} />
      <primitive object={new AxesHelper(1)} />
      <Suspense fallback={null}>
        <HuginModel scale={0.3} />
      </Suspense>
      {sensors.map((sensor, i) => (
        <group
          key={i}
          position={sensor.position}
          onPointerOver={() => setHoveredSensor(sensor)}
          onPointerOut={() => setHoveredSensor(null)}
        >
          <mesh>
            <sphereGeometry args={[0.0075, 16, 16]} />
            <meshStandardMaterial color={sensor.color} />
          </mesh>
          {hoveredSensor?.name === sensor.name && (
            <Html
              distanceFactor={4}
              style={{
                background: "#20232a",
                padding: "4px 6px",
                borderRadius: "3px",
                fontSize: "0.65rem",
                color: "#61dafb",
                fontFamily: "monospace",
              }}
            >
              <div>{sensor.name}</div>
              <div style={{ fontSize: "0.6rem" }}>
                X: {sensor.position[0]}
                <br />
                Y: {sensor.position[1]}
                <br />
                Z: {sensor.position[2]}
              </div>
            </Html>
          )}
        </group>
      ))}
      <OrbitControls />
    </Canvas>
  );
}

function exportCSV(sensors) {
  const header = "Sensor,X,Y,Z,Type,Serial,Notes";
  const rows = sensors.map(
    (s) =>
      `${s.name},${s.position[0]},${s.position[1]},${s.position[2]},${s.metadata.type},${s.metadata.serial},${s.metadata.notes}`
  );
  const blob = new Blob([header + "\n" + rows.join("\n")], {
    type: "text/csv",
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = "offsets.csv";
  a.click();
}

function importCSV(text) {
  try {
    const lines = text.trim().split("\n").slice(1);
    return lines
      .filter(line => line.trim()) // Remove empty lines
      .map((row, index) => {
        const [name, x, y, z, type = "", serial = "", notes = ""] = row.split(",");

        // Validate required fields
        if (!name || isNaN(parseFloat(x)) || isNaN(parseFloat(y)) || isNaN(parseFloat(z))) {
          throw new Error(`Invalid data at row ${index + 2}: ${row}`);
        }

        return {
          name: name.trim(),
          position: [parseFloat(x), parseFloat(y), parseFloat(z)],
          color: "#" + Math.floor(Math.random() * 16777215).toString(16),
          metadata: {
            type: type.trim(),
            serial: serial.trim(),
            notes: notes.trim()
          },
        };
      });
  } catch (error) {
    console.error("CSV import error:", error);
    alert(`Failed to import CSV: ${error.message}`);
    return [];
  }
}

import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import html2canvas from "html2canvas";

function HuginVisualiser() {
  const [isDarkTheme, setIsDarkTheme] = useState(true);

  const defaultVehicle = {
    name: "HUGIN 3000 - DEMO CONFIG",
    serial: "HU3000-001",
    software: "MissionSuite v10.4.1",
    operator: "Ocean Infinity",
    buildDate: "2022-11-15",
    depthRating: "3000 m",
    lastService: "2024-03-05",
    sensors: [
      "EM2040 MkII Multibeam (1°x1°, 400 kHz, 50–500m range) [1]",
      "HiSAS 1030 Synthetic Aperture Sonar (2–5cm resolution) [2]",
      "Kongsberg MGC R3 INS (0.01° heading accuracy) [3]",
      "Valeport miniSVS Sound Velocity Sensor (±0.02 m/s accuracy) [4]",
      "Sub-bottom profiler (Innomar SES-2000 light, 10–15 kHz) [5]",
      "USBL responder (Kongsberg cNODE Mini) [6]",
      "Magnetometer (Geometric G-882, towed, -1000m rated) [7]",
      "CTD package (Seabird SBE 49 FastCAT) [8]",
      "FLIR M364C Topside Camera [9]",
    ],
    references: [
      "[1] Kongsberg Maritime. (2020). EM 2040 MkII Multibeam Echo Sounder Product Specification.",
      "[2] Kongsberg Maritime. (2019). HiSAS 1030 Synthetic Aperture Sonar Technical Manual.",
      "[3] Kongsberg Maritime. (2021). MGC R3 Inertial Navigation System Datasheet.",
      "[4] Valeport Ltd. (2018). miniSVS Sound Velocity Sensor User Manual.",
      "[5] Innomar Technologie GmbH. (2017). SES-2000 light Sub-bottom Profiler Specifications.",
      "[6] Kongsberg Maritime. (2020). cNODE Mini USBL Transponder Technical Documentation.",
      "[7] Geometrics Inc. (2019). G-882 Marine Magnetometer Operating Manual.",
      "[8] Sea-Bird Scientific. (2020). SBE 49 FastCAT CTD Sensor Specifications.",
      "[9] FLIR Systems. (2021). M364C Maritime Thermal Camera Datasheet."
    ],
    calibration: {
      EM2040: "2024-02-22",
      INS: "2024-01-14",
      SVP: "2024-01-03",
      Magnetometer: "2023-12-19",
    },
  };

  const defaultSensors = [
    {
      name: "INS - MGC R3",
      position: [0, 0, 0],
      color: "#ff5050",
      metadata: {
        type: "Inertial Navigation System",
        serial: "R3-231A",
        notes: "Located at CRP (0,0,0)",
      },
    },
    {
      name: "EM2040 TX",
      position: [0.3, 0, -0.2],
      color: "#3399ff",
      metadata: {
        type: "Multibeam Transmitter",
        serial: "TX-2040A",
        notes: "Centre belly mounted, forward of CRP",
      },
    },
  ];

  const [sensors, setSensors] = useState(defaultSensors);
  const [hoveredSensor, setHoveredSensor] = useState(null);

  const updateSensorName = (i, name) => {
    const next = [...sensors];
    next[i].name = name;
    setSensors(next);
  };

  const updateSensorPosition = (i, axis, value) => {
    const next = [...sensors];
    next[i].position[axis] = parseFloat(value);
    setSensors(next);
  };

  const updateSensorMetadata = (i, field, value) => {
    const next = [...sensors];
    next[i].metadata[field] = value;
    setSensors(next);
  };

  const deleteSensor = (i) => {
    const next = sensors.filter((_, idx) => idx !== i);
    setSensors(next);
  };

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (event) => {
      const sensors = importCSV(event.target.result);
      setSensors(sensors);
    };
  };

  const generatePDF = async () => {
    try {
      const doc = new jsPDF({
        orientation: "portrait",
        unit: "pt",
        format: "a4",
      });
      const now = new Date();
      const dateString = now.toLocaleString();

    doc.setFontSize(16);
    doc.text("AUV Payload and Sensor Offset Report", 40, 50);
    doc.setFontSize(10);
    doc.text(`Generated: ${dateString}`, 40, 70);
    doc.text(`Vessel: ${defaultVehicle.name}`, 40, 85);
    doc.text(`Operator: ${defaultVehicle.operator}`, 40, 100);

    doc.setFontSize(12);
    doc.text("Sensor Summary:", 40, 125);
    const tableY = 135;
    const headers = [["Sensor", "Spec", "Calibration"]];
    const rows = [
      [
        "EM2040 MkII",
        "1°x1°, 400kHz, 50–500m",
        defaultVehicle.calibration.EM2040,
      ],
      ["HiSAS 1030", "2–5cm resolution, dual side", "N/A"],
      [
        "MGC R3 INS",
        "0.01° heading, 0.02° roll/pitch",
        defaultVehicle.calibration.INS,
      ],
      ["miniSVS", "±0.02 m/s accuracy", defaultVehicle.calibration.SVP],
      [
        "Magnetometer",
        "Towed, -1000m rated",
        defaultVehicle.calibration.Magnetometer,
      ],
    ];
    doc.autoTable({ head: headers, body: rows, startY: tableY });

    const offsetStartY = doc.lastAutoTable.finalY + 20;
    doc.text("Sensor Offsets:", 40, offsetStartY);
    const offsetRows = sensors.map((s) => [
      s.name,
      s.position.join(", "),
      s.metadata.type,
      s.metadata.serial,
    ]);
    doc.autoTable({
      head: [["Name", "Position (X,Y,Z)", "Type", "Serial"]],
      body: offsetRows,
      startY: offsetStartY + 10,
    });

    const canvasEl = document.querySelector("canvas");
    if (canvasEl) {
      const imgData = await html2canvas(canvasEl).then((c) =>
        c.toDataURL("image/png")
      );
      doc.addPage();
      doc.setFontSize(12);
      doc.text("3D Model Snapshot:", 40, 40);
      doc.addImage(imgData, "PNG", 40, 60, 500, 300);
    }

      doc.save(`auv_sensor_report_${now.getTime()}.pdf`);
    } catch (error) {
      console.error("PDF generation failed:", error);
      alert("Failed to generate PDF. Please try again.");
    }
  };
  const themeStyles = useMemo(() => ({
    background: isDarkTheme ? "#0b0c10" : "#f5f5f5",
    color: isDarkTheme ? "#f8f8f8" : "#333",
    cardBackground: isDarkTheme ? "#14181f" : "#fff",
    inputBackground: isDarkTheme ? "#111" : "#fff",
    borderColor: isDarkTheme ? "#333" : "#ddd",
  }), [isDarkTheme]);

  return (
    <div
      style={{
        padding: "1.5rem 2rem",
        background: themeStyles.background,
        minHeight: "100vh",
        fontFamily: "Segoe UI, sans-serif",
        color: themeStyles.color,
        transition: "background-color 0.3s ease, color 0.3s ease",
      }}
    >
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "1rem" }}>
        <AppHeader vehicle={defaultVehicle} />
        <button
          onClick={() => setIsDarkTheme(!isDarkTheme)}
          style={{
            padding: "0.5rem 1rem",
            background: themeStyles.cardBackground,
            color: themeStyles.color,
            border: `1px solid ${themeStyles.borderColor}`,
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          {isDarkTheme ? "☀️ Light" : "🌙 Dark"}
        </button>
      </div>

      <details style={{ marginBottom: "2rem" }}>
        <summary
          style={{
            fontSize: "1rem",
            fontWeight: "bold",
            cursor: "pointer",
            padding: "0.5rem 0",
          }}
        >
          Payload Sensor Summary
        </summary>
        <table
          style={{
            width: "100%",
            background: "#14181f",
            borderCollapse: "collapse",
            color: "#eee",
            fontSize: "0.9rem",
            marginTop: "0.5rem",
          }}
        >
          <thead>
            <tr>
              <th
                style={{
                  textAlign: "left",
                  padding: "0.5rem",
                  borderBottom: "1px solid #333",
                }}
              >
                Sensor
              </th>
              <th
                style={{
                  textAlign: "left",
                  padding: "0.5rem",
                  borderBottom: "1px solid #333",
                }}
              >
                Specification
              </th>
              <th
                style={{
                  textAlign: "left",
                  padding: "0.5rem",
                  borderBottom: "1px solid #333",
                }}
              >
                Calibration
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ padding: "0.5rem" }}>EM2040 MkII Multibeam</td>
              <td style={{ padding: "0.5rem" }}>
                1°x1°, 400 kHz, 50–500 m range
              </td>
              <td style={{ padding: "0.5rem" }}>
                {defaultVehicle.calibration.EM2040}
              </td>
            </tr>
            <tr>
              <td style={{ padding: "0.5rem" }}>
                HiSAS 1030 Synthetic Aperture Sonar
              </td>
              <td style={{ padding: "0.5rem" }}>
                2–5 cm resolution, dual side array
              </td>
              <td style={{ padding: "0.5rem" }}>N/A</td>
            </tr>
            <tr>
              <td style={{ padding: "0.5rem" }}>Kongsberg MGC R3 INS</td>
              <td style={{ padding: "0.5rem" }}>
                0.01° heading, 0.02° roll/pitch
              </td>
              <td style={{ padding: "0.5rem" }}>
                {defaultVehicle.calibration.INS}
              </td>
            </tr>
            <tr>
              <td style={{ padding: "0.5rem" }}>Valeport miniSVS</td>
              <td style={{ padding: "0.5rem" }}>±0.02 m/s accuracy</td>
              <td style={{ padding: "0.5rem" }}>
                {defaultVehicle.calibration.SVP}
              </td>
            </tr>
            <tr>
              <td style={{ padding: "0.5rem" }}>
                Geometric G-882 Magnetometer
              </td>
              <td style={{ padding: "0.5rem" }}>Towed, 1000 m depth rated</td>
              <td style={{ padding: "0.5rem" }}>
                {defaultVehicle.calibration.Magnetometer}
              </td>
            </tr>
          </tbody>
        </table>
      </details>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          marginBottom: "1rem",
        }}
      >
        <button
          onClick={() =>
            setSensors([
              ...sensors,
              {
                name: `Sensor_${sensors.length + 1}`,
                position: [0, 0, 0],
                color: "#" + Math.floor(Math.random() * 16777215).toString(16),
                metadata: { type: "", serial: "", notes: "" },
              },
            ])
          }
          style={{
            padding: "0.5rem 1rem",
            background: "#1f2632",
            color: "#fff",
            border: "1px solid #333",
            borderRadius: "4px",
          }}
        >
          + Add Sensor
        </button>
      </div>
      <SensorEditor
        sensors={sensors}
        updateSensorName={updateSensorName}
        updateSensorPosition={updateSensorPosition}
        updateSensorMetadata={updateSensorMetadata}
        deleteSensor={deleteSensor}
      />
      <div style={{ marginTop: "2rem" }}>
        <h3 style={{ marginBottom: "0.5rem" }}>3D Sensor Placement</h3>
        <Scene
          sensors={sensors}
          hoveredSensor={hoveredSensor}
          setHoveredSensor={setHoveredSensor}
        />
        <div style={{ display: "flex", gap: "1rem", marginTop: "1rem" }}>
          <button
            onClick={() => exportCSV(sensors)}
            style={{
              padding: "0.5rem 1rem",
              background: "#1f2632",
              color: "#9fd1ff",
              border: "1px solid #333",
              borderRadius: "4px",
            }}
          >
            Export CSV
          </button>
          <label
            style={{ display: "flex", alignItems: "center", cursor: "pointer" }}
          >
            <input
              type="file"
              accept=".csv"
              onChange={handleFileUpload}
              style={{ display: "none" }}
            />
            <span
              style={{
                background: "#1f2632",
                color: "#ddd",
                padding: "0.5rem 1rem",
                borderRadius: "4px",
                border: "1px solid #333",
              }}
            >
              Import CSV
            </span>
          </label>
        </div>
        <button
          onClick={generatePDF}
          style={{
            padding: "0.5rem 1rem",
            background: "#1f2632",
            color: "#fff",
            border: "1px solid #444",
            borderRadius: "4px",
          }}
        >
          Export PDF Report
        </button>
      </div>

      <References references={defaultVehicle.references} />
    </div>
  );
}

// Wrap the main component in an ErrorBoundary
const WrappedHuginVisualiser = () => (
  <ErrorBoundary>
    <HuginVisualiser />
  </ErrorBoundary>
);

export default WrappedHuginVisualiser;
