{"version": 3, "file": "RoomEnvironment.js", "sources": ["../../src/environments/RoomEnvironment.ts"], "sourcesContent": ["/**\n * https://github.com/google/model-viewer/blob/master/packages/model-viewer/src/three-components/EnvironmentScene.ts\n */\n\nimport * as THREE from 'three'\n\nfunction RoomEnvironment() {\n  const scene = new THREE.Scene()\n\n  const geometry = new THREE.BoxGeometry()\n  geometry.deleteAttribute('uv')\n\n  const roomMaterial = new THREE.MeshStandardMaterial({ side: THREE.BackSide })\n  const boxMaterial = new THREE.MeshStandardMaterial()\n\n  const mainLight = new THREE.PointLight(0xffffff, 5.0, 28, 2)\n  mainLight.position.set(0.418, 16.199, 0.3)\n  scene.add(mainLight)\n\n  const room = new THREE.Mesh(geometry, roomMaterial)\n  room.position.set(-0.757, 13.219, 0.717)\n  room.scale.set(31.713, 28.305, 28.591)\n  scene.add(room)\n\n  const box1 = new THREE.Mesh(geometry, boxMaterial)\n  box1.position.set(-10.906, 2.009, 1.846)\n  box1.rotation.set(0, -0.195, 0)\n  box1.scale.set(2.328, 7.905, 4.651)\n  scene.add(box1)\n\n  const box2 = new THREE.Mesh(geometry, boxMaterial)\n  box2.position.set(-5.607, -0.754, -0.758)\n  box2.rotation.set(0, 0.994, 0)\n  box2.scale.set(1.97, 1.534, 3.955)\n  scene.add(box2)\n\n  const box3 = new THREE.Mesh(geometry, boxMaterial)\n  box3.position.set(6.167, 0.857, 7.803)\n  box3.rotation.set(0, 0.561, 0)\n  box3.scale.set(3.927, 6.285, 3.687)\n  scene.add(box3)\n\n  const box4 = new THREE.Mesh(geometry, boxMaterial)\n  box4.position.set(-2.017, 0.018, 6.124)\n  box4.rotation.set(0, 0.333, 0)\n  box4.scale.set(2.002, 4.566, 2.064)\n  scene.add(box4)\n\n  const box5 = new THREE.Mesh(geometry, boxMaterial)\n  box5.position.set(2.291, -0.756, -2.621)\n  box5.rotation.set(0, -0.286, 0)\n  box5.scale.set(1.546, 1.552, 1.496)\n  scene.add(box5)\n\n  const box6 = new THREE.Mesh(geometry, boxMaterial)\n  box6.position.set(-2.193, -0.369, -5.547)\n  box6.rotation.set(0, 0.516, 0)\n  box6.scale.set(3.875, 3.487, 2.986)\n  scene.add(box6)\n\n  // -x right\n  const light1 = new THREE.Mesh(geometry, createAreaLightMaterial(50))\n  light1.position.set(-16.116, 14.37, 8.208)\n  light1.scale.set(0.1, 2.428, 2.739)\n  scene.add(light1)\n\n  // -x left\n  const light2 = new THREE.Mesh(geometry, createAreaLightMaterial(50))\n  light2.position.set(-16.109, 18.021, -8.207)\n  light2.scale.set(0.1, 2.425, 2.751)\n  scene.add(light2)\n\n  // +x\n  const light3 = new THREE.Mesh(geometry, createAreaLightMaterial(17))\n  light3.position.set(14.904, 12.198, -1.832)\n  light3.scale.set(0.15, 4.265, 6.331)\n  scene.add(light3)\n\n  // +z\n  const light4 = new THREE.Mesh(geometry, createAreaLightMaterial(43))\n  light4.position.set(-0.462, 8.89, 14.52)\n  light4.scale.set(4.38, 5.441, 0.088)\n  scene.add(light4)\n\n  // -z\n  const light5 = new THREE.Mesh(geometry, createAreaLightMaterial(20))\n  light5.position.set(3.235, 11.486, -12.541)\n  light5.scale.set(2.5, 2.0, 0.1)\n  scene.add(light5)\n\n  // +y\n  const light6 = new THREE.Mesh(geometry, createAreaLightMaterial(100))\n  light6.position.set(0.0, 20.0, 0.0)\n  light6.scale.set(1.0, 0.1, 1.0)\n  scene.add(light6)\n\n  function createAreaLightMaterial(intensity: number) {\n    const material = new THREE.MeshBasicMaterial()\n    material.color.setScalar(intensity)\n    return material\n  }\n\n  return scene\n}\n\nexport { RoomEnvironment }\n"], "names": [], "mappings": ";AAMA,SAAS,kBAAkB;AACnB,QAAA,QAAQ,IAAI,MAAM;AAElB,QAAA,WAAW,IAAI,MAAM;AAC3B,WAAS,gBAAgB,IAAI;AAEvB,QAAA,eAAe,IAAI,MAAM,qBAAqB,EAAE,MAAM,MAAM,UAAU;AACtE,QAAA,cAAc,IAAI,MAAM;AAE9B,QAAM,YAAY,IAAI,MAAM,WAAW,UAAU,GAAK,IAAI,CAAC;AAC3D,YAAU,SAAS,IAAI,OAAO,QAAQ,GAAG;AACzC,QAAM,IAAI,SAAS;AAEnB,QAAM,OAAO,IAAI,MAAM,KAAK,UAAU,YAAY;AAClD,OAAK,SAAS,IAAI,QAAQ,QAAQ,KAAK;AACvC,OAAK,MAAM,IAAI,QAAQ,QAAQ,MAAM;AACrC,QAAM,IAAI,IAAI;AAEd,QAAM,OAAO,IAAI,MAAM,KAAK,UAAU,WAAW;AACjD,OAAK,SAAS,IAAI,SAAS,OAAO,KAAK;AACvC,OAAK,SAAS,IAAI,GAAG,QAAQ,CAAC;AAC9B,OAAK,MAAM,IAAI,OAAO,OAAO,KAAK;AAClC,QAAM,IAAI,IAAI;AAEd,QAAM,OAAO,IAAI,MAAM,KAAK,UAAU,WAAW;AACjD,OAAK,SAAS,IAAI,QAAQ,QAAQ,MAAM;AACxC,OAAK,SAAS,IAAI,GAAG,OAAO,CAAC;AAC7B,OAAK,MAAM,IAAI,MAAM,OAAO,KAAK;AACjC,QAAM,IAAI,IAAI;AAEd,QAAM,OAAO,IAAI,MAAM,KAAK,UAAU,WAAW;AACjD,OAAK,SAAS,IAAI,OAAO,OAAO,KAAK;AACrC,OAAK,SAAS,IAAI,GAAG,OAAO,CAAC;AAC7B,OAAK,MAAM,IAAI,OAAO,OAAO,KAAK;AAClC,QAAM,IAAI,IAAI;AAEd,QAAM,OAAO,IAAI,MAAM,KAAK,UAAU,WAAW;AACjD,OAAK,SAAS,IAAI,QAAQ,OAAO,KAAK;AACtC,OAAK,SAAS,IAAI,GAAG,OAAO,CAAC;AAC7B,OAAK,MAAM,IAAI,OAAO,OAAO,KAAK;AAClC,QAAM,IAAI,IAAI;AAEd,QAAM,OAAO,IAAI,MAAM,KAAK,UAAU,WAAW;AACjD,OAAK,SAAS,IAAI,OAAO,QAAQ,MAAM;AACvC,OAAK,SAAS,IAAI,GAAG,QAAQ,CAAC;AAC9B,OAAK,MAAM,IAAI,OAAO,OAAO,KAAK;AAClC,QAAM,IAAI,IAAI;AAEd,QAAM,OAAO,IAAI,MAAM,KAAK,UAAU,WAAW;AACjD,OAAK,SAAS,IAAI,QAAQ,QAAQ,MAAM;AACxC,OAAK,SAAS,IAAI,GAAG,OAAO,CAAC;AAC7B,OAAK,MAAM,IAAI,OAAO,OAAO,KAAK;AAClC,QAAM,IAAI,IAAI;AAGd,QAAM,SAAS,IAAI,MAAM,KAAK,UAAU,wBAAwB,EAAE,CAAC;AACnE,SAAO,SAAS,IAAI,SAAS,OAAO,KAAK;AACzC,SAAO,MAAM,IAAI,KAAK,OAAO,KAAK;AAClC,QAAM,IAAI,MAAM;AAGhB,QAAM,SAAS,IAAI,MAAM,KAAK,UAAU,wBAAwB,EAAE,CAAC;AACnE,SAAO,SAAS,IAAI,SAAS,QAAQ,MAAM;AAC3C,SAAO,MAAM,IAAI,KAAK,OAAO,KAAK;AAClC,QAAM,IAAI,MAAM;AAGhB,QAAM,SAAS,IAAI,MAAM,KAAK,UAAU,wBAAwB,EAAE,CAAC;AACnE,SAAO,SAAS,IAAI,QAAQ,QAAQ,MAAM;AAC1C,SAAO,MAAM,IAAI,MAAM,OAAO,KAAK;AACnC,QAAM,IAAI,MAAM;AAGhB,QAAM,SAAS,IAAI,MAAM,KAAK,UAAU,wBAAwB,EAAE,CAAC;AACnE,SAAO,SAAS,IAAI,QAAQ,MAAM,KAAK;AACvC,SAAO,MAAM,IAAI,MAAM,OAAO,KAAK;AACnC,QAAM,IAAI,MAAM;AAGhB,QAAM,SAAS,IAAI,MAAM,KAAK,UAAU,wBAAwB,EAAE,CAAC;AACnE,SAAO,SAAS,IAAI,OAAO,QAAQ,OAAO;AAC1C,SAAO,MAAM,IAAI,KAAK,GAAK,GAAG;AAC9B,QAAM,IAAI,MAAM;AAGhB,QAAM,SAAS,IAAI,MAAM,KAAK,UAAU,wBAAwB,GAAG,CAAC;AACpE,SAAO,SAAS,IAAI,GAAK,IAAM,CAAG;AAClC,SAAO,MAAM,IAAI,GAAK,KAAK,CAAG;AAC9B,QAAM,IAAI,MAAM;AAEhB,WAAS,wBAAwB,WAAmB;AAC5C,UAAA,WAAW,IAAI,MAAM;AAClB,aAAA,MAAM,UAAU,SAAS;AAC3B,WAAA;AAAA,EACT;AAEO,SAAA;AACT;"}