"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toCodePoints = exports.fromCodePoint = exports.splitGraphemes = exports.GraphemeBreaker = void 0;
var GraphemeBreak_1 = require("./GraphemeBreak");
Object.defineProperty(exports, "GraphemeBreaker", { enumerable: true, get: function () { return GraphemeBreak_1.GraphemeBreaker; } });
Object.defineProperty(exports, "splitGraphemes", { enumerable: true, get: function () { return GraphemeBreak_1.splitGraphemes; } });
Object.defineProperty(exports, "fromCodePoint", { enumerable: true, get: function () { return GraphemeBreak_1.fromCodePoint; } });
Object.defineProperty(exports, "toCodePoints", { enumerable: true, get: function () { return GraphemeBreak_1.toCodePoints; } });
//# sourceMappingURL=index.js.map