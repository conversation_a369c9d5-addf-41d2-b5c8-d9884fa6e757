#!/usr/bin/env node

import fs from 'fs'
import path from 'path'

console.log('🔍 AUV Sensor Studio Debug Check')
console.log('=================================\n')

// Check project structure
console.log('📁 Project Structure:')
const requiredFiles = [
  'package.json',
  'vite.config.js', 
  'index.html',
  'src/App.jsx',
  'src/main.jsx',
  'src/context/SensorContext.jsx',
  'src/components/SensorStudio.jsx',
  'src/components/Viewport3D.jsx',
  'src/components/SensorMarkers.jsx',
  'src/components/SensorPanel.jsx',
  'src/components/Toolbar.jsx',
  'src/components/ErrorBoundary.jsx',
  'src/utils/export.js'
]

requiredFiles.forEach(file => {
  const exists = fs.existsSync(file)
  console.log(`${exists ? '✅' : '❌'} ${file}`)
})

// Check dependencies
console.log('\n📦 Dependencies:')
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const deps = { ...packageJson.dependencies, ...packageJson.devDependencies }
  
  const criticalDeps = [
    'react',
    'react-dom', 
    '@react-three/fiber',
    '@react-three/drei',
    'three',
    'vite',
    '@vitejs/plugin-react'
  ]
  
  criticalDeps.forEach(dep => {
    const installed = fs.existsSync(`node_modules/${dep}`)
    const version = deps[dep] || 'Not in package.json'
    console.log(`${installed ? '✅' : '❌'} ${dep}: ${version}`)
  })
} catch (error) {
  console.log('❌ Error reading package.json:', error.message)
}

// Check for common issues
console.log('\n🔧 Common Issues Check:')

// Check for TypeScript files
const tsFiles = []
function findTsFiles(dir) {
  const files = fs.readdirSync(dir)
  files.forEach(file => {
    const fullPath = path.join(dir, file)
    if (fs.statSync(fullPath).isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      findTsFiles(fullPath)
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      tsFiles.push(fullPath)
    }
  })
}

try {
  findTsFiles('.')
  if (tsFiles.length > 0) {
    console.log('⚠️  TypeScript files found (should be .jsx):')
    tsFiles.forEach(file => console.log(`   ${file}`))
  } else {
    console.log('✅ No TypeScript files found')
  }
} catch (error) {
  console.log('❌ Error checking for TypeScript files')
}

// Check Vite config
try {
  const viteConfig = fs.readFileSync('vite.config.js', 'utf8')
  if (viteConfig.includes('3001')) {
    console.log('✅ Vite configured for port 3001')
  } else {
    console.log('⚠️  Vite port might not be set to 3001')
  }
} catch (error) {
  console.log('❌ Error reading vite.config.js')
}

// Check for node_modules
const nodeModulesExists = fs.existsSync('node_modules')
console.log(`${nodeModulesExists ? '✅' : '❌'} node_modules directory`)

// Browser compatibility check
console.log('\n🌐 Browser Compatibility:')
console.log('✅ Chrome/Edge: Supported')
console.log('✅ Firefox: Supported') 
console.log('✅ Safari: Supported (modern versions)')
console.log('❌ Internet Explorer: Not supported')

// Performance tips
console.log('\n⚡ Performance Tips:')
console.log('• Use Chrome DevTools for 3D debugging')
console.log('• Check WebGL support: chrome://gpu/')
console.log('• Monitor memory usage in DevTools')
console.log('• Use React DevTools for component debugging')

// Next steps
console.log('\n🚀 Next Steps:')
console.log('1. Run: npm install (if dependencies missing)')
console.log('2. Run: npm run dev')
console.log('3. Open: http://localhost:3001')
console.log('4. Check browser console for errors')
console.log('5. Look for the debug panel in top-right corner')

console.log('\n📱 Application Features to Test:')
console.log('• 3D scene loads with AUV model')
console.log('• Two default sensors visible (red and blue spheres)')
console.log('• Click sensors to select them')
console.log('• Drag sensors to move them')
console.log('• Use mouse to rotate/pan/zoom 3D view')
console.log('• Add new sensors with toolbar button')
console.log('• Edit sensor properties in right panel')
console.log('• Export/import CSV data')

console.log('\n🐛 If Issues Persist:')
console.log('• Check browser console (F12)')
console.log('• Verify WebGL support')
console.log('• Try different browser')
console.log('• Clear browser cache')
console.log('• Restart development server')

console.log('\n✨ Debug complete!')
