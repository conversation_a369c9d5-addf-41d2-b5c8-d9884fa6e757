import React from 'react'
import { useAUVConfig } from '../context/AUVConfigContext'
import { useSensors } from '../context/SensorContext'
import {
  PowerTab,
  PayloadTab,
  CommunicationTab,
  OperatingTab
} from './AUVConfigTabs'
import {
  MissionTab,
  SafetyTab,
  DataTab
} from './MissionTabs'

export function AUVConfigPanel() {
  const { 
    isConfigPanelOpen, 
    toggleConfigPanel, 
    activeTab, 
    setActiveTab,
    auvConfig,
    missionConfig,
    updateAUVConfig,
    updateMissionConfig,
    validateConfiguration,
    validationErrors,
    saveConfiguration,
    loadConfiguration,
    savedConfigurations,
    currentConfigName
  } = useAUVConfig()
  
  const { sensors } = useSensors()

  if (!isConfigPanelOpen) return null

  const tabs = [
    { id: 'vehicle', label: '🚁 Vehicle', icon: '🚁' },
    { id: 'sonar', label: '📡 Sonar', icon: '📡' },
    { id: 'navigation', label: '🧭 Navigation', icon: '🧭' },
    { id: 'power', label: '🔋 Power', icon: '🔋' },
    { id: 'payload', label: '📦 Payload', icon: '📦' },
    { id: 'communication', label: '📻 Comms', icon: '📻' },
    { id: 'operating', label: '⚙️ Operating', icon: '⚙️' },
    { id: 'mission', label: '🎯 Mission', icon: '🎯' },
    { id: 'safety', label: '🛡️ Safety', icon: '🛡️' },
    { id: 'data', label: '💾 Data', icon: '💾' }
  ]

  const handleSave = () => {
    const configName = prompt('Enter configuration name:')
    if (configName) {
      saveConfiguration(configName, sensors)
      alert(`Configuration "${configName}" saved successfully!`)
    }
  }

  const handleLoad = () => {
    if (savedConfigurations.length === 0) {
      alert('No saved configurations available')
      return
    }
    
    const configNames = savedConfigurations.map(c => c.name).join('\n')
    const configName = prompt(`Select configuration to load:\n${configNames}`)
    
    if (configName && savedConfigurations.find(c => c.name === configName)) {
      loadConfiguration(configName)
      alert(`Configuration "${configName}" loaded successfully!`)
    }
  }

  const handleValidate = () => {
    const isValid = validateConfiguration()
    if (isValid) {
      alert('✅ Configuration validation passed!')
    } else {
      alert(`❌ Configuration validation failed:\n${validationErrors.join('\n')}`)
    }
  }

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      right: 0,
      width: '500px',
      height: '100vh',
      background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
      border: '2px solid #cc5500',
      borderRadius: '8px 0 0 8px',
      zIndex: 1000,
      display: 'flex',
      flexDirection: 'column',
      boxShadow: '-5px 0 20px rgba(204, 85, 0, 0.3)'
    }}>
      {/* Header */}
      <div style={{
        padding: '1rem',
        borderBottom: '2px solid #cc5500',
        background: 'linear-gradient(90deg, #cc5500, #dd6600)',
        color: 'white'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, fontSize: '1.2rem', fontWeight: 'bold' }}>
            ⚙️ AUV Configuration Manager
          </h2>
          <button
            onClick={toggleConfigPanel}
            style={{
              background: 'rgba(255,255,255,0.2)',
              border: 'none',
              color: 'white',
              padding: '0.5rem',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '1rem'
            }}
          >
            ✕
          </button>
        </div>
        
        {/* Configuration Management */}
        <div style={{ 
          display: 'flex', 
          gap: '0.5rem', 
          marginTop: '0.5rem',
          fontSize: '0.8rem'
        }}>
          <button onClick={handleSave} style={buttonStyle}>💾 Save</button>
          <button onClick={handleLoad} style={buttonStyle}>📁 Load</button>
          <button onClick={handleValidate} style={buttonStyle}>✅ Validate</button>
          <span style={{ 
            marginLeft: 'auto', 
            fontSize: '0.7rem',
            opacity: 0.8
          }}>
            {currentConfigName || 'Unsaved Configuration'}
          </span>
        </div>
      </div>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        flexWrap: 'wrap',
        background: '#333',
        borderBottom: '1px solid #555',
        padding: '0.5rem'
      }}>
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            style={{
              background: activeTab === tab.id ? '#cc5500' : 'transparent',
              color: activeTab === tab.id ? 'white' : '#ccc',
              border: '1px solid #555',
              padding: '0.3rem 0.6rem',
              margin: '0.1rem',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '0.7rem',
              transition: 'all 0.2s'
            }}
          >
            {tab.icon}
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '1rem',
        color: 'white'
      }}>
        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <div style={{
            background: 'rgba(255, 0, 0, 0.1)',
            border: '1px solid #ff4444',
            borderRadius: '4px',
            padding: '0.5rem',
            marginBottom: '1rem',
            fontSize: '0.8rem'
          }}>
            <strong>⚠️ Validation Errors:</strong>
            <ul style={{ margin: '0.5rem 0', paddingLeft: '1rem' }}>
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Tab Content */}
        {activeTab === 'vehicle' && <VehicleTab config={auvConfig.vehicle} updateConfig={updateAUVConfig} />}
        {activeTab === 'sonar' && <SonarTab config={auvConfig.sonar} updateConfig={updateAUVConfig} />}
        {activeTab === 'navigation' && <NavigationTab config={auvConfig.navigation} updateConfig={updateAUVConfig} />}
        {activeTab === 'power' && <PowerTab config={auvConfig.power} updateConfig={updateAUVConfig} />}
        {activeTab === 'payload' && <PayloadTab config={auvConfig.payload} updateConfig={updateAUVConfig} />}
        {activeTab === 'communication' && <CommunicationTab config={auvConfig.communication} updateConfig={updateAUVConfig} />}
        {activeTab === 'operating' && <OperatingTab config={auvConfig.operating} updateConfig={updateAUVConfig} />}
        {activeTab === 'mission' && <MissionTab config={missionConfig.profile} updateConfig={updateMissionConfig} />}
        {activeTab === 'safety' && <SafetyTab config={missionConfig.safety} updateConfig={updateMissionConfig} />}
        {activeTab === 'data' && <DataTab config={missionConfig.dataCollection} updateConfig={updateMissionConfig} />}
      </div>
    </div>
  )
}

const buttonStyle = {
  background: 'rgba(255,255,255,0.1)',
  border: '1px solid rgba(255,255,255,0.3)',
  color: 'white',
  padding: '0.2rem 0.5rem',
  borderRadius: '3px',
  cursor: 'pointer',
  fontSize: '0.7rem'
}

const inputStyle = {
  width: '100%',
  padding: '0.5rem',
  background: '#444',
  border: '1px solid #666',
  borderRadius: '4px',
  color: 'white',
  fontSize: '0.9rem'
}

const labelStyle = {
  display: 'block',
  marginBottom: '0.3rem',
  fontSize: '0.8rem',
  fontWeight: 'bold',
  color: '#cc5500'
}

// Vehicle Configuration Tab
function VehicleTab({ config, updateConfig }) {
  const handleChange = (field, value) => {
    updateConfig('vehicle', { [field]: value })
  }

  return (
    <div>
      <h3 style={{ color: '#cc5500', marginBottom: '1rem' }}>🚁 Vehicle Identification</h3>
      
      <div style={{ marginBottom: '1rem' }}>
        <label style={labelStyle}>Vehicle ID:</label>
        <input
          type="text"
          value={config.id}
          onChange={(e) => handleChange('id', e.target.value)}
          style={inputStyle}
          placeholder="e.g., AUV-001"
        />
      </div>

      <div style={{ marginBottom: '1rem' }}>
        <label style={labelStyle}>Serial Number:</label>
        <input
          type="text"
          value={config.serial}
          onChange={(e) => handleChange('serial', e.target.value)}
          style={inputStyle}
          placeholder="e.g., HG3000-2023-001"
        />
      </div>

      <div style={{ marginBottom: '1rem' }}>
        <label style={labelStyle}>Vehicle Name:</label>
        <input
          type="text"
          value={config.name}
          onChange={(e) => handleChange('name', e.target.value)}
          style={inputStyle}
          placeholder="e.g., Deep Explorer"
        />
      </div>

      <div style={{ marginBottom: '1rem' }}>
        <label style={labelStyle}>Model:</label>
        <select
          value={config.model}
          onChange={(e) => handleChange('model', e.target.value)}
          style={inputStyle}
        >
          <option value="hugin3000">Hugin 3000</option>
          <option value="hugin1000">Hugin 1000</option>
          <option value="custom">Custom</option>
        </select>
      </div>

      <div style={{ marginBottom: '1rem' }}>
        <label style={labelStyle}>Operator:</label>
        <input
          type="text"
          value={config.operator}
          onChange={(e) => handleChange('operator', e.target.value)}
          style={inputStyle}
          placeholder="e.g., Marine Research Institute"
        />
      </div>

      <div style={{ marginBottom: '1rem' }}>
        <label style={labelStyle}>Last Maintenance:</label>
        <input
          type="date"
          value={config.lastMaintenance}
          onChange={(e) => handleChange('lastMaintenance', e.target.value)}
          style={inputStyle}
        />
      </div>

      <div style={{ marginBottom: '1rem' }}>
        <label style={labelStyle}>Total Operating Hours:</label>
        <input
          type="number"
          value={config.totalHours}
          onChange={(e) => handleChange('totalHours', parseFloat(e.target.value) || 0)}
          style={inputStyle}
          placeholder="0"
        />
      </div>
    </div>
  )
}

// Sonar Configuration Tab
function SonarTab({ config, updateConfig }) {
  const handleChange = (section, field, value) => {
    updateConfig('sonar', {
      ...config,
      [section]: {
        ...config[section],
        [field]: value
      }
    })
  }

  return (
    <div>
      <h3 style={{ color: '#cc5500', marginBottom: '1rem' }}>📡 Sonar Equipment</h3>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Primary Side Scan Sonar</h4>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Type:</label>
          <select
            value={config.primarySonar.type}
            onChange={(e) => handleChange('primarySonar', 'type', e.target.value)}
            style={inputStyle}
          >
            <option value="Side Scan Sonar">Side Scan Sonar</option>
            <option value="Synthetic Aperture Sonar">Synthetic Aperture Sonar</option>
            <option value="Interferometric Sonar">Interferometric Sonar</option>
          </select>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Model:</label>
          <input
            type="text"
            value={config.primarySonar.model}
            onChange={(e) => handleChange('primarySonar', 'model', e.target.value)}
            style={inputStyle}
            placeholder="e.g., EdgeTech 2205"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Frequency:</label>
          <select
            value={config.primarySonar.frequency}
            onChange={(e) => handleChange('primarySonar', 'frequency', e.target.value)}
            style={inputStyle}
          >
            <option value="100kHz">100 kHz</option>
            <option value="400kHz">400 kHz</option>
            <option value="900kHz">900 kHz</option>
            <option value="Dual Frequency">Dual Frequency</option>
          </select>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Range:</label>
          <input
            type="text"
            value={config.primarySonar.range}
            onChange={(e) => handleChange('primarySonar', 'range', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 150m"
          />
        </div>
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Multibeam Echosounder</h4>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>
            <input
              type="checkbox"
              checked={config.multibeam.enabled}
              onChange={(e) => handleChange('multibeam', 'enabled', e.target.checked)}
              style={{ marginRight: '0.5rem' }}
            />
            Enable Multibeam
          </label>
        </div>

        {config.multibeam.enabled && (
          <>
            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Model:</label>
              <input
                type="text"
                value={config.multibeam.model}
                onChange={(e) => handleChange('multibeam', 'model', e.target.value)}
                style={inputStyle}
                placeholder="e.g., Kongsberg EM 2040"
              />
            </div>

            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Frequency:</label>
              <select
                value={config.multibeam.frequency}
                onChange={(e) => handleChange('multibeam', 'frequency', e.target.value)}
                style={inputStyle}
              >
                <option value="200kHz">200 kHz</option>
                <option value="300kHz">300 kHz</option>
                <option value="400kHz">400 kHz</option>
              </select>
            </div>

            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Number of Beams:</label>
              <input
                type="number"
                value={config.multibeam.beams}
                onChange={(e) => handleChange('multibeam', 'beams', parseInt(e.target.value) || 256)}
                style={inputStyle}
                placeholder="256"
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

// Navigation Configuration Tab
function NavigationTab({ config, updateConfig }) {
  const handleChange = (section, field, value) => {
    updateConfig('navigation', {
      ...config,
      [section]: {
        ...config[section],
        [field]: value
      }
    })
  }

  return (
    <div>
      <h3 style={{ color: '#cc5500', marginBottom: '1rem' }}>🧭 Navigation Systems</h3>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Inertial Navigation System (INS)</h4>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Type:</label>
          <select
            value={config.ins.type}
            onChange={(e) => handleChange('ins', 'type', e.target.value)}
            style={inputStyle}
          >
            <option value="Fiber Optic Gyro">Fiber Optic Gyro</option>
            <option value="Ring Laser Gyro">Ring Laser Gyro</option>
            <option value="MEMS IMU">MEMS IMU</option>
          </select>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Model:</label>
          <input
            type="text"
            value={config.ins.model}
            onChange={(e) => handleChange('ins', 'model', e.target.value)}
            style={inputStyle}
            placeholder="e.g., Honeywell HG1700"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Accuracy:</label>
          <input
            type="text"
            value={config.ins.accuracy}
            onChange={(e) => handleChange('ins', 'accuracy', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 0.01°/hr"
          />
        </div>
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Doppler Velocity Log (DVL)</h4>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>
            <input
              type="checkbox"
              checked={config.dvl.enabled}
              onChange={(e) => handleChange('dvl', 'enabled', e.target.checked)}
              style={{ marginRight: '0.5rem' }}
            />
            Enable DVL
          </label>
        </div>

        {config.dvl.enabled && (
          <>
            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Model:</label>
              <input
                type="text"
                value={config.dvl.model}
                onChange={(e) => handleChange('dvl', 'model', e.target.value)}
                style={inputStyle}
                placeholder="e.g., Teledyne RDI Navigator"
              />
            </div>

            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Frequency:</label>
              <select
                value={config.dvl.frequency}
                onChange={(e) => handleChange('dvl', 'frequency', e.target.value)}
                style={inputStyle}
              >
                <option value="300kHz">300 kHz</option>
                <option value="600kHz">600 kHz</option>
                <option value="1200kHz">1200 kHz</option>
              </select>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
