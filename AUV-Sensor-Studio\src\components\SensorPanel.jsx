import React, { useState } from 'react'
import { useSensors } from '../context/SensorContext'

const SENSOR_TYPES = [
  { value: 'navigation', label: 'Navigation', color: '#ff4444' },
  { value: 'sonar', label: 'Sonar', color: '#4444ff' },
  { value: 'camera', label: 'Camera', color: '#44ff44' },
  { value: 'sensor', label: 'Sensor', color: '#ffff44' },
  { value: 'generic', label: 'Generic', color: '#ff44ff' }
]

function SensorEditor({ sensor }) {
  const { updateSensor, deleteSensor } = useSensors()
  const [localPosition, setLocalPosition] = useState(sensor.position)

  const handlePositionChange = (axis, value) => {
    const newPosition = [...localPosition]
    newPosition[axis] = parseFloat(value) || 0
    setLocalPosition(newPosition)
    updateSensor(sensor.id, { position: newPosition })
  }

  const handleNameChange = (name) => {
    updateSensor(sensor.id, { name })
  }

  const handleTypeChange = (type) => {
    const typeInfo = SENSOR_TYPES.find(t => t.value === type)
    updateSensor(sensor.id, { 
      type,
      color: typeInfo?.color || sensor.color
    })
  }

  return (
    <div style={{
      padding: '1rem',
      background: '#1a1a1a',
      border: '1px solid #333',
      borderRadius: '4px',
      marginBottom: '0.5rem'
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        marginBottom: '1rem'
      }}>
        <div
          style={{
            width: '12px',
            height: '12px',
            borderRadius: '50%',
            background: sensor.color,
            marginRight: '0.5rem'
          }}
        />
        <input
          type="text"
          value={sensor.name}
          onChange={(e) => handleNameChange(e.target.value)}
          style={{
            flex: 1,
            background: '#333',
            border: '1px solid #555',
            color: 'white',
            padding: '0.25rem 0.5rem',
            borderRadius: '4px',
            fontSize: '0.9rem'
          }}
        />
        <button
          onClick={() => deleteSensor(sensor.id)}
          style={{
            marginLeft: '0.5rem',
            padding: '0.25rem 0.5rem',
            background: '#ff4444',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '0.8rem'
          }}
        >
          ×
        </button>
      </div>

      {/* Type Selection */}
      <div style={{ marginBottom: '1rem' }}>
        <label style={{
          display: 'block',
          marginBottom: '0.25rem',
          fontSize: '0.8rem',
          color: '#ccc'
        }}>
          Type
        </label>
        <select
          value={sensor.type}
          onChange={(e) => handleTypeChange(e.target.value)}
          style={{
            width: '100%',
            background: '#333',
            border: '1px solid #555',
            color: 'white',
            padding: '0.25rem',
            borderRadius: '4px'
          }}
        >
          {SENSOR_TYPES.map(type => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>
      </div>

      {/* Position Controls */}
      <div>
        <label style={{
          display: 'block',
          marginBottom: '0.5rem',
          fontSize: '0.8rem',
          color: '#ccc'
        }}>
          Position (meters)
        </label>
        
        {['X (Starboard)', 'Y (Forward)', 'Z (Down)'].map((label, axis) => (
          <div key={axis} style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: '0.25rem'
          }}>
            <span style={{
              width: '80px',
              fontSize: '0.8rem',
              color: ['#ff4444', '#44ff44', '#4444ff'][axis]
            }}>
              {label}:
            </span>
            <input
              type="number"
              step="0.01"
              value={localPosition[axis]}
              onChange={(e) => handlePositionChange(axis, e.target.value)}
              style={{
                flex: 1,
                background: '#333',
                border: '1px solid #555',
                color: 'white',
                padding: '0.25rem',
                borderRadius: '4px',
                fontSize: '0.9rem'
              }}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

export function SensorPanel() {
  const { sensors, selectedSensor, addSensor } = useSensors()
  const [filter, setFilter] = useState('')

  const filteredSensors = sensors.filter(sensor =>
    sensor.name.toLowerCase().includes(filter.toLowerCase()) ||
    sensor.type.toLowerCase().includes(filter.toLowerCase())
  )

  const selectedSensorData = sensors.find(s => s.id === selectedSensor)

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      background: '#111'
    }}>
      {/* Panel Header */}
      <div style={{
        padding: '1rem',
        borderBottom: '1px solid #333'
      }}>
        <h2 style={{
          fontSize: '1.1rem',
          marginBottom: '1rem',
          color: '#fff'
        }}>
          Sensor Configuration
        </h2>
        
        <input
          type="text"
          placeholder="Search sensors..."
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          style={{
            width: '100%',
            background: '#333',
            border: '1px solid #555',
            color: 'white',
            padding: '0.5rem',
            borderRadius: '4px',
            marginBottom: '1rem'
          }}
        />
      </div>

      {/* Sensor List */}
      <div style={{
        flex: 1,
        overflowY: 'auto',
        padding: '1rem'
      }}>
        {filteredSensors.length === 0 ? (
          <div style={{
            textAlign: 'center',
            color: '#666',
            padding: '2rem'
          }}>
            {filter ? 'No sensors match your search' : 'No sensors configured'}
          </div>
        ) : (
          filteredSensors.map(sensor => (
            <SensorEditor key={sensor.id} sensor={sensor} />
          ))
        )}
      </div>

      {/* Selected Sensor Details */}
      {selectedSensorData && (
        <div style={{
          borderTop: '1px solid #333',
          padding: '1rem',
          background: '#0a0a0a'
        }}>
          <h3 style={{
            fontSize: '0.9rem',
            marginBottom: '0.5rem',
            color: '#4CAF50'
          }}>
            Selected: {selectedSensorData.name}
          </h3>
          <div style={{ fontSize: '0.8rem', color: '#ccc' }}>
            Position: {selectedSensorData.position.map(p => p.toFixed(3)).join(', ')}
          </div>
          <div style={{ fontSize: '0.8rem', color: '#ccc' }}>
            Type: {selectedSensorData.type}
          </div>
        </div>
      )}
    </div>
  )
}
