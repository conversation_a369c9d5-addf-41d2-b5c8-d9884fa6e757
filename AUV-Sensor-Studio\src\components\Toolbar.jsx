import React, { useRef } from 'react'
import { useSensors } from '../context/SensorContext'
import { exportToCSV } from '../utils/export'
import { STEPParser } from '../utils/stepParser'

export function Toolbar() {
  const { sensors, addSensor, showLabels, toggleLabels, setCustomModel, modelType } = useSensors()
  const fileInputRef = useRef()
  const modelInputRef = useRef()

  const handleAddSensor = () => {
    addSensor({
      name: `Sensor ${sensors.length + 1}`,
      position: [0, 0, 0],
      type: 'generic'
    })
  }

  const handleExport = () => {
    exportToCSV(sensors)
  }

  const handleImport = (event) => {
    const file = event.target.files[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const csv = e.target.result
        const lines = csv.split('\n').filter(line => line.trim())
        const headers = lines[0].split(',')

        lines.slice(1).forEach(line => {
          const values = line.split(',')
          if (values.length >= 4) {
            addSensor({
              name: values[0]?.trim() || 'Imported Sensor',
              position: [
                parseFloat(values[1]) || 0,
                parseFloat(values[2]) || 0,
                parseFloat(values[3]) || 0
              ],
              type: values[4]?.trim() || 'generic'
            })
          }
        })
      } catch (error) {
        alert('Error importing CSV: ' + error.message)
      }
    }
    reader.readAsText(file)
    event.target.value = '' // Reset input
  }

  const handleModelUpload = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    // Check file type
    const fileName = file.name.toLowerCase()
    let modelType = 'gltf'

    if (fileName.endsWith('.step') || fileName.endsWith('.stp')) {
      modelType = 'step'

      // Try to parse STEP file natively
      const parser = new STEPParser()
      const result = await parser.parseSTEPFile(file)

      if (result.success) {
        alert(`STEP file parsed successfully!\n\nFound:\n- ${result.metadata.pointCount} points\n- ${result.metadata.entityCount} entities\n\nNote: This is a basic parser. For complex models, consider converting to GLTF for better performance.`)

        // Create a special URL for STEP data
        const stepData = JSON.stringify(result)
        const blob = new Blob([stepData], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        setCustomModel(url, 'step-parsed')
      } else {
        alert(`STEP parsing failed: ${result.error}\n\nRecommendation: Convert to GLTF/GLB format using:\n- Online converters\n- CAD software export\n- Blender import/export`)
      }
    } else if (!fileName.endsWith('.gltf') && !fileName.endsWith('.glb')) {
      alert('Please select a STEP (.step, .stp) or GLTF (.gltf, .glb) file.')
      return
    } else {
      // Handle GLTF/GLB files normally
      const url = URL.createObjectURL(file)
      setCustomModel(url, modelType)
    }

    event.target.value = '' // Reset input
  }

  return (
    <div style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: '48px',
      background: 'rgba(0,0,0,0.9)',
      backdropFilter: 'blur(10px)',
      borderBottom: '1px solid #333',
      display: 'flex',
      alignItems: 'center',
      padding: '0 1rem',
      gap: '0.5rem',
      zIndex: 100
    }}>
      <h1 style={{
        fontSize: '1.1rem',
        fontWeight: '600',
        marginRight: '2rem'
      }}>
        AUV Sensor Studio
      </h1>

      <button
        onClick={handleAddSensor}
        style={{
          padding: '0.5rem 1rem',
          background: '#4CAF50',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '0.9rem'
        }}
      >
        + Add Sensor
      </button>

      <button
        onClick={handleExport}
        style={{
          padding: '0.5rem 1rem',
          background: '#333',
          color: 'white',
          border: '1px solid #555',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '0.9rem'
        }}
      >
        Export CSV
      </button>

      <button
        onClick={() => fileInputRef.current?.click()}
        style={{
          padding: '0.5rem 1rem',
          background: '#333',
          color: 'white',
          border: '1px solid #555',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '0.9rem'
        }}
      >
        Import CSV
      </button>

      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        onChange={handleImport}
        style={{ display: 'none' }}
      />

      <button
        onClick={() => modelInputRef.current?.click()}
        style={{
          padding: '0.5rem 1rem',
          background: modelType === 'fallback' ? '#333' : '#2196F3',
          color: 'white',
          border: '1px solid #555',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '0.9rem'
        }}
      >
        {modelType === 'fallback' ? '📁 Load AUV Model' : '✅ Custom Model'}
      </button>

      <input
        ref={modelInputRef}
        type="file"
        accept=".step,.stp,.gltf,.glb"
        onChange={handleModelUpload}
        style={{ display: 'none' }}
      />

      <button
        onClick={toggleLabels}
        style={{
          padding: '0.5rem 1rem',
          background: showLabels ? '#4CAF50' : '#333',
          color: 'white',
          border: '1px solid #555',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '0.9rem'
        }}
      >
        {showLabels ? '🏷️ Labels ON' : '🏷️ Labels OFF'}
      </button>

      <div style={{ flex: 1 }} />

      <div style={{
        fontSize: '0.85rem',
        color: '#888',
        display: 'flex',
        gap: '1rem'
      }}>
        <span style={{ color: '#ff4444' }}>X: Starboard</span>
        <span style={{ color: '#44ff44' }}>Y: Forward</span>
        <span style={{ color: '#4444ff' }}>Z: Down</span>
      </div>
    </div>
  )
}
