{"version": 3, "file": "GlitchPass.js", "sources": ["../../src/postprocessing/GlitchPass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport {\n  DataTexture,\n  FloatType,\n  MathUtils,\n  RedFormat,\n  ShaderMaterial,\n  UniformsUtils,\n  WebGLRenderTarget,\n  WebGLRenderer,\n  IUniform,\n} from 'three'\nimport { DigitalGlitch } from '../shaders/DigitalGlitch'\n\nclass GlitchPass extends Pass {\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n  public goWild: boolean\n  public curF: number\n  public randX!: number\n\n  public uniforms: Record<keyof typeof DigitalGlitch['uniforms'], IUniform<any>>\n\n  constructor(dt_size = 64) {\n    super()\n    this.uniforms = UniformsUtils.clone(DigitalGlitch.uniforms)\n    this.uniforms['tDisp'].value = this.generateHeightmap(dt_size)\n\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: DigitalGlitch.vertexShader,\n      fragmentShader: DigitalGlitch.fragmentShader,\n    })\n\n    this.fsQuad = new FullScreenQuad(this.material)\n    this.goWild = false\n    this.curF = 0\n    this.generateTrigger()\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    /*, deltaTime, maskActive */\n  ): void {\n    this.uniforms['tDiffuse'].value = readBuffer.texture\n    this.uniforms['seed'].value = Math.random() //default seeding\n    this.uniforms['byp'].value = 0\n\n    if (this.curF % this.randX == 0 || this.goWild == true) {\n      this.uniforms['amount'].value = Math.random() / 30\n      this.uniforms['angle'].value = MathUtils.randFloat(-Math.PI, Math.PI)\n      this.uniforms['seed_x'].value = MathUtils.randFloat(-1, 1)\n      this.uniforms['seed_y'].value = MathUtils.randFloat(-1, 1)\n      this.uniforms['distortion_x'].value = MathUtils.randFloat(0, 1)\n      this.uniforms['distortion_y'].value = MathUtils.randFloat(0, 1)\n      this.curF = 0\n      this.generateTrigger()\n    } else if (this.curF % this.randX < this.randX / 5) {\n      this.uniforms['amount'].value = Math.random() / 90\n      this.uniforms['angle'].value = MathUtils.randFloat(-Math.PI, Math.PI)\n      this.uniforms['distortion_x'].value = MathUtils.randFloat(0, 1)\n      this.uniforms['distortion_y'].value = MathUtils.randFloat(0, 1)\n      this.uniforms['seed_x'].value = MathUtils.randFloat(-0.3, 0.3)\n      this.uniforms['seed_y'].value = MathUtils.randFloat(-0.3, 0.3)\n    } else if (this.goWild == false) {\n      this.uniforms['byp'].value = 1\n    }\n\n    this.curF++\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      if (this.clear) renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n  }\n\n  generateTrigger(): void {\n    this.randX = MathUtils.randInt(120, 240)\n  }\n\n  generateHeightmap(dt_size: number): DataTexture {\n    const data_arr = new Float32Array(dt_size * dt_size)\n    const length = dt_size * dt_size\n\n    for (let i = 0; i < length; i++) {\n      const val = MathUtils.randFloat(0, 1)\n      data_arr[i] = val\n    }\n\n    const texture = new DataTexture(data_arr, dt_size, dt_size, RedFormat, FloatType)\n    texture.needsUpdate = true\n    return texture\n  }\n}\n\nexport { GlitchPass }\n"], "names": [], "mappings": ";;;;;;;;;AAcA,MAAM,mBAAmB,KAAK;AAAA,EAS5B,YAAY,UAAU,IAAI;AAClB;AATD;AACA;AACA;AACA;AACA;AAEA;AAIL,SAAK,WAAW,cAAc,MAAM,cAAc,QAAQ;AAC1D,SAAK,SAAS,OAAO,EAAE,QAAQ,KAAK,kBAAkB,OAAO;AAExD,SAAA,WAAW,IAAI,eAAe;AAAA,MACjC,UAAU,KAAK;AAAA,MACf,cAAc,cAAc;AAAA,MAC5B,gBAAgB,cAAc;AAAA,IAAA,CAC/B;AAED,SAAK,SAAS,IAAI,eAAe,KAAK,QAAQ;AAC9C,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACvB;AAAA,EAEO,OACL,UACA,aACA,YAEM;AACN,SAAK,SAAS,UAAU,EAAE,QAAQ,WAAW;AAC7C,SAAK,SAAS,MAAM,EAAE,QAAQ,KAAK;AAC9B,SAAA,SAAS,KAAK,EAAE,QAAQ;AAE7B,QAAI,KAAK,OAAO,KAAK,SAAS,KAAK,KAAK,UAAU,MAAM;AACtD,WAAK,SAAS,QAAQ,EAAE,QAAQ,KAAK,OAAW,IAAA;AAC3C,WAAA,SAAS,OAAO,EAAE,QAAQ,UAAU,UAAU,CAAC,KAAK,IAAI,KAAK,EAAE;AACpE,WAAK,SAAS,QAAQ,EAAE,QAAQ,UAAU,UAAU,IAAI,CAAC;AACzD,WAAK,SAAS,QAAQ,EAAE,QAAQ,UAAU,UAAU,IAAI,CAAC;AACzD,WAAK,SAAS,cAAc,EAAE,QAAQ,UAAU,UAAU,GAAG,CAAC;AAC9D,WAAK,SAAS,cAAc,EAAE,QAAQ,UAAU,UAAU,GAAG,CAAC;AAC9D,WAAK,OAAO;AACZ,WAAK,gBAAgB;AAAA,IAAA,WACZ,KAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ,GAAG;AAClD,WAAK,SAAS,QAAQ,EAAE,QAAQ,KAAK,OAAW,IAAA;AAC3C,WAAA,SAAS,OAAO,EAAE,QAAQ,UAAU,UAAU,CAAC,KAAK,IAAI,KAAK,EAAE;AACpE,WAAK,SAAS,cAAc,EAAE,QAAQ,UAAU,UAAU,GAAG,CAAC;AAC9D,WAAK,SAAS,cAAc,EAAE,QAAQ,UAAU,UAAU,GAAG,CAAC;AAC9D,WAAK,SAAS,QAAQ,EAAE,QAAQ,UAAU,UAAU,MAAM,GAAG;AAC7D,WAAK,SAAS,QAAQ,EAAE,QAAQ,UAAU,UAAU,MAAM,GAAG;AAAA,IAAA,WACpD,KAAK,UAAU,OAAO;AAC1B,WAAA,SAAS,KAAK,EAAE,QAAQ;AAAA,IAC/B;AAEK,SAAA;AAEL,QAAI,KAAK,gBAAgB;AACvB,eAAS,gBAAgB,IAAI;AACxB,WAAA,OAAO,OAAO,QAAQ;AAAA,IAAA,OACtB;AACL,eAAS,gBAAgB,WAAW;AACpC,UAAI,KAAK;AAAO,iBAAS,MAAM;AAC1B,WAAA,OAAO,OAAO,QAAQ;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,kBAAwB;AACtB,SAAK,QAAQ,UAAU,QAAQ,KAAK,GAAG;AAAA,EACzC;AAAA,EAEA,kBAAkB,SAA8B;AAC9C,UAAM,WAAW,IAAI,aAAa,UAAU,OAAO;AACnD,UAAM,SAAS,UAAU;AAEzB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAM,MAAM,UAAU,UAAU,GAAG,CAAC;AACpC,eAAS,CAAC,IAAI;AAAA,IAChB;AAEA,UAAM,UAAU,IAAI,YAAY,UAAU,SAAS,SAAS,WAAW,SAAS;AAChF,YAAQ,cAAc;AACf,WAAA;AAAA,EACT;AACF;"}