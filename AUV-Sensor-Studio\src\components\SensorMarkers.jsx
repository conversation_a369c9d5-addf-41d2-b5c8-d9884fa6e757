import React, { useRef, useState, useCallback } from 'react'
import { useFrame, useThree } from '@react-three/fiber'
import { Html } from '@react-three/drei'
import * as THREE from 'three'
import { useSensors } from '../context/SensorContext'

function SensorMarker({ sensor, isSelected, onSelect, onPositionChange, showLabel }) {
  const meshRef = useRef()
  const [hovered, setHovered] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const { camera, gl } = useThree()

  useFrame((state) => {
    if (meshRef.current && !isDragging) {
      // Gentle pulsing animation for selected sensor
      if (isSelected) {
        const scale = 1 + Math.sin(state.clock.elapsedTime * 3) * 0.1
        meshRef.current.scale.setScalar(scale)
      } else {
        meshRef.current.scale.setScalar(hovered ? 1.2 : 1)
      }
    }
  })

  const handlePointerDown = useCallback((event) => {
    event.stopPropagation()
    setIsDragging(true)
    onSelect(sensor.id)
    gl.domElement.style.cursor = 'grabbing'
  }, [onSelect, sensor.id, gl.domElement])

  const handlePointerUp = useCallback(() => {
    setIsDragging(false)
    gl.domElement.style.cursor = hovered ? 'pointer' : 'auto'
  }, [hovered, gl.domElement])

  const handlePointerMove = useCallback((event) => {
    if (!isDragging) return

    event.stopPropagation()

    // Convert screen coordinates to world coordinates
    const rect = gl.domElement.getBoundingClientRect()
    const x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    const y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    // Simple projection to world space (this is a simplified version)
    const vector = new THREE.Vector3(x, y, 0.5)
    vector.unproject(camera)

    const dir = vector.sub(camera.position).normalize()
    const distance = -camera.position.z / dir.z
    const pos = camera.position.clone().add(dir.multiplyScalar(distance))

    onPositionChange(sensor.id, [pos.x, pos.y, sensor.position[2]])
  }, [isDragging, gl.domElement, camera, onPositionChange, sensor.id, sensor.position])

  React.useEffect(() => {
    if (isDragging) {
      const handleGlobalPointerMove = (event) => handlePointerMove(event)
      const handleGlobalPointerUp = () => handlePointerUp()

      document.addEventListener('pointermove', handleGlobalPointerMove)
      document.addEventListener('pointerup', handleGlobalPointerUp)

      return () => {
        document.removeEventListener('pointermove', handleGlobalPointerMove)
        document.removeEventListener('pointerup', handleGlobalPointerUp)
      }
    }
  }, [isDragging, handlePointerMove, handlePointerUp])

  return (
    <group position={sensor.position}>
      <mesh
        ref={meshRef}
        onPointerEnter={(e) => {
          e.stopPropagation()
          setHovered(true)
          gl.domElement.style.cursor = 'pointer'
        }}
        onPointerLeave={(e) => {
          e.stopPropagation()
          setHovered(false)
          gl.domElement.style.cursor = 'auto'
        }}
        onPointerDown={handlePointerDown}
        castShadow
      >
        <sphereGeometry args={[0.04, 20, 20]} />
        <meshStandardMaterial
          color={sensor.color}
          emissive={isSelected ? sensor.color : (hovered ? sensor.color : '#000000')}
          emissiveIntensity={isSelected ? 0.4 : (hovered ? 0.2 : 0)}
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>

      {/* Outer glow ring for selected sensors */}
      {isSelected && (
        <mesh>
          <ringGeometry args={[0.06, 0.08, 16]} />
          <meshBasicMaterial
            color={sensor.color}
            transparent
            opacity={0.6}
            side={2}
          />
        </mesh>
      )}

      {/* Dynamic Sensor Label - Conditionally Visible */}
      {showLabel && (
        <Html
        position={[0, 0.08, 0]}
        center
        distanceFactor={6}
        style={{
          background: isSelected ? 'rgba(76, 175, 80, 0.9)' : 'rgba(0,0,0,0.85)',
          color: 'white',
          padding: '0.4rem 0.6rem',
          borderRadius: '6px',
          fontSize: '0.75rem',
          whiteSpace: 'nowrap',
          pointerEvents: 'none',
          border: `2px solid ${sensor.color}`,
          boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
          fontFamily: 'monospace',
          minWidth: '120px',
          textAlign: 'center'
        }}
      >
        <div style={{
          fontWeight: 'bold',
          marginBottom: '0.2rem',
          color: isSelected ? '#fff' : sensor.color
        }}>
          {sensor.name}
        </div>
        <div style={{
          fontSize: '0.65rem',
          lineHeight: '1.2',
          color: '#e0e0e0'
        }}>
          <div>X: {sensor.position[0].toFixed(3)}m</div>
          <div>Y: {sensor.position[1].toFixed(3)}m</div>
          <div>Z: {sensor.position[2].toFixed(3)}m</div>
        </div>
        {isSelected && (
          <div style={{
            fontSize: '0.6rem',
            marginTop: '0.2rem',
            color: '#fff',
            fontWeight: 'bold'
          }}>
            SELECTED
          </div>
        )}
      </Html>
      )}

      {/* Connection line to origin */}
      <line>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={2}
            array={new Float32Array([
              0, 0, 0,
              -sensor.position[0], -sensor.position[1], -sensor.position[2]
            ])}
            itemSize={3}
          />
        </bufferGeometry>
        <lineBasicMaterial color={sensor.color} opacity={0.3} transparent />
      </line>
    </group>
  )
}

export function SensorMarkers({ sensors }) {
  const { selectedSensor, selectSensor, updateSensor, showLabels } = useSensors()

  const handlePositionChange = (id, newPosition) => {
    updateSensor(id, { position: newPosition })
  }

  return (
    <group>
      {sensors.map(sensor => (
        <SensorMarker
          key={sensor.id}
          sensor={sensor}
          isSelected={selectedSensor === sensor.id}
          onSelect={selectSensor}
          onPositionChange={handlePositionChange}
          showLabel={showLabels}
        />
      ))}
    </group>
  )
}
