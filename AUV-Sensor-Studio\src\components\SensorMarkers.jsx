import React, { useRef, useState } from 'react'
import { useFrame, useThree } from '@react-three/fiber'
import { Html } from '@react-three/drei'
import * as THREE from 'three'
import { useSensors } from '../context/SensorContext'

function SensorMarker({ sensor, isSelected, onSelect, onPositionChange }) {
  const meshRef = useRef()
  const [hovered, setHovered] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const { camera, gl } = useThree()

  useFrame((state) => {
    if (meshRef.current && !isDragging) {
      // Gentle pulsing animation for selected sensor
      if (isSelected) {
        const scale = 1 + Math.sin(state.clock.elapsedTime * 3) * 0.1
        meshRef.current.scale.setScalar(scale)
      } else {
        meshRef.current.scale.setScalar(hovered ? 1.2 : 1)
      }
    }
  })

  const handlePointerDown = (event) => {
    event.stopPropagation()
    setIsDragging(true)
    onSelect(sensor.id)
    gl.domElement.style.cursor = 'grabbing'
  }

  const handlePointerUp = () => {
    setIsDragging(false)
    gl.domElement.style.cursor = hovered ? 'pointer' : 'auto'
  }

  const handlePointerMove = (event) => {
    if (!isDragging) return
    
    event.stopPropagation()
    
    // Convert screen coordinates to world coordinates
    const rect = gl.domElement.getBoundingClientRect()
    const x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    const y = -((event.clientY - rect.top) / rect.height) * 2 + 1
    
    // Simple projection to world space (this is a simplified version)
    const vector = new THREE.Vector3(x, y, 0.5)
    vector.unproject(camera)
    
    const dir = vector.sub(camera.position).normalize()
    const distance = -camera.position.z / dir.z
    const pos = camera.position.clone().add(dir.multiplyScalar(distance))
    
    onPositionChange(sensor.id, [pos.x, pos.y, sensor.position[2]])
  }

  React.useEffect(() => {
    if (isDragging) {
      const handleGlobalPointerMove = (event) => handlePointerMove(event)
      const handleGlobalPointerUp = () => handlePointerUp()
      
      document.addEventListener('pointermove', handleGlobalPointerMove)
      document.addEventListener('pointerup', handleGlobalPointerUp)
      
      return () => {
        document.removeEventListener('pointermove', handleGlobalPointerMove)
        document.removeEventListener('pointerup', handleGlobalPointerUp)
      }
    }
  }, [isDragging])

  return (
    <group position={sensor.position}>
      <mesh
        ref={meshRef}
        onPointerEnter={(e) => {
          e.stopPropagation()
          setHovered(true)
          gl.domElement.style.cursor = 'pointer'
        }}
        onPointerLeave={(e) => {
          e.stopPropagation()
          setHovered(false)
          gl.domElement.style.cursor = 'auto'
        }}
        onPointerDown={handlePointerDown}
        castShadow
      >
        <sphereGeometry args={[0.03, 16, 16]} />
        <meshStandardMaterial
          color={sensor.color}
          emissive={isSelected ? sensor.color : '#000000'}
          emissiveIntensity={isSelected ? 0.3 : 0}
          metalness={0.7}
          roughness={0.3}
        />
      </mesh>

      {/* Sensor label */}
      {(hovered || isSelected) && (
        <Html
          position={[0, 0.1, 0]}
          center
          distanceFactor={8}
          style={{
            background: 'rgba(0,0,0,0.8)',
            color: 'white',
            padding: '0.25rem 0.5rem',
            borderRadius: '4px',
            fontSize: '0.7rem',
            whiteSpace: 'nowrap',
            pointerEvents: 'none',
            border: `1px solid ${sensor.color}`
          }}
        >
          <div>{sensor.name}</div>
          <div style={{ fontSize: '0.6rem', opacity: 0.8 }}>
            {sensor.position.map(p => p.toFixed(2)).join(', ')}
          </div>
        </Html>
      )}

      {/* Connection line to origin */}
      <line>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={2}
            array={new Float32Array([
              0, 0, 0,
              -sensor.position[0], -sensor.position[1], -sensor.position[2]
            ])}
            itemSize={3}
          />
        </bufferGeometry>
        <lineBasicMaterial color={sensor.color} opacity={0.3} transparent />
      </line>
    </group>
  )
}

export function SensorMarkers({ sensors }) {
  const { selectedSensor, selectSensor, updateSensor } = useSensors()

  const handlePositionChange = (id, newPosition) => {
    updateSensor(id, { position: newPosition })
  }

  return (
    <group>
      {sensors.map(sensor => (
        <SensorMarker
          key={sensor.id}
          sensor={sensor}
          isSelected={selectedSensor === sensor.id}
          onSelect={selectSensor}
          onPositionChange={handlePositionChange}
        />
      ))}
    </group>
  )
}
