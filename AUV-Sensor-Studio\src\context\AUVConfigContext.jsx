import React, { createContext, useContext, useReducer } from 'react'

// Default AUV configuration structure
const defaultConfig = {
  // Vehicle Identification
  vehicle: {
    id: '',
    serial: '',
    model: 'hugin3000',
    name: '',
    operator: '',
    lastMaintenance: '',
    totalHours: 0
  },
  
  // Sonar Equipment
  sonar: {
    primarySonar: {
      type: 'Side Scan Sonar',
      model: '',
      frequency: '400kHz',
      range: '150m',
      resolution: '3cm',
      swathWidth: '300m'
    },
    multibeam: {
      enabled: false,
      model: '',
      frequency: '200kHz',
      beams: 256,
      swathAngle: '120°'
    },
    subBottom: {
      enabled: false,
      model: '',
      frequency: '3.5kHz',
      penetration: '50m'
    }
  },
  
  // Navigation Systems
  navigation: {
    ins: {
      type: 'Fiber Optic Gyro',
      model: '',
      accuracy: '0.01°/hr',
      alignment: 'Auto'
    },
    dvl: {
      enabled: true,
      model: '',
      frequency: '300kHz',
      range: '200m',
      accuracy: '0.1%'
    },
    usbl: {
      compatible: true,
      frequency: '18-34kHz',
      range: '3000m',
      accuracy: '0.2%'
    },
    gps: {
      enabled: true,
      type: 'Dual Frequency',
      accuracy: '1m'
    }
  },
  
  // Power Systems
  power: {
    battery: {
      type: 'Lithium Ion',
      capacity: '10kWh',
      voltage: '48V',
      cells: 16,
      chemistry: 'LiFePO4'
    },
    endurance: {
      cruise: '20 hours',
      survey: '16 hours',
      emergency: '24 hours'
    },
    consumption: {
      propulsion: '200W',
      sensors: '150W',
      navigation: '50W',
      communication: '30W',
      total: '430W'
    }
  },
  
  // Payload Configuration
  payload: {
    maxCapacity: '50kg',
    currentWeight: '0kg',
    maxVolume: '0.2m³',
    currentVolume: '0m³',
    centerOfGravity: { x: 0, y: 0, z: 0 },
    buoyancy: 'Neutral'
  },
  
  // Communication Systems
  communication: {
    acoustic: {
      enabled: true,
      model: '',
      frequency: '12kHz',
      range: '3000m',
      dataRate: '80bps'
    },
    wifi: {
      enabled: true,
      standard: '802.11ac',
      range: '100m',
      dataRate: '1Gbps'
    },
    satellite: {
      enabled: false,
      type: 'Iridium',
      dataRate: '2.4kbps'
    },
    emergency: {
      beacon: true,
      frequency: '406MHz',
      battery: '48 hours'
    }
  },
  
  // Operating Parameters
  operating: {
    maxDepth: '3000m',
    cruiseSpeed: '1.5 m/s',
    surveySpeed: '1.0 m/s',
    maxSpeed: '2.5 m/s',
    turnRadius: '10m',
    ascentRate: '0.5 m/s',
    descentRate: '0.5 m/s'
  }
}

// Mission planning configuration
const defaultMission = {
  // Mission Profile
  profile: {
    type: 'Survey',
    pattern: 'Lawn Mower',
    area: '1km²',
    duration: '8 hours',
    priority: 'High',
    classification: 'Unclassified'
  },
  
  // Environmental Parameters
  environment: {
    waterDepth: '100m',
    current: {
      speed: '0.2 m/s',
      direction: '045°',
      variation: 'Low'
    },
    temperature: '15°C',
    salinity: '35 PSU',
    visibility: '10m',
    seaState: '2'
  },
  
  // Safety Parameters
  safety: {
    abortDepth: '3100m',
    returnTriggers: {
      lowBattery: '20%',
      lostComms: '30 minutes',
      sensorFailure: 'Critical Only'
    },
    emergencyProcedures: {
      surfaceImmediate: true,
      activateBeacon: true,
      dataUpload: true
    },
    safetyMargin: '10%'
  },
  
  // Data Collection
  dataCollection: {
    loggingInterval: '1 second',
    fileFormat: 'XTF',
    storageCapacity: '2TB',
    compression: 'Enabled',
    backup: 'Redundant',
    qualityControl: 'Real-time'
  },
  
  // Pre-mission Checklist
  checklist: {
    systemChecks: false,
    sensorCalibration: false,
    navigationAlignment: false,
    communicationTest: false,
    batteryCheck: false,
    payloadSecure: false,
    missionPlan: false,
    weatherCheck: false
  }
}

const initialState = {
  auvConfig: { ...defaultConfig },
  missionConfig: { ...defaultMission },
  savedConfigurations: [],
  currentConfigName: '',
  isConfigPanelOpen: false,
  activeTab: 'vehicle',
  validationErrors: [],
  configurationHistory: []
}

function auvConfigReducer(state, action) {
  switch (action.type) {
    case 'TOGGLE_CONFIG_PANEL':
      return {
        ...state,
        isConfigPanelOpen: !state.isConfigPanelOpen
      }
    
    case 'SET_ACTIVE_TAB':
      return {
        ...state,
        activeTab: action.payload
      }
    
    case 'UPDATE_AUV_CONFIG':
      return {
        ...state,
        auvConfig: {
          ...state.auvConfig,
          [action.section]: {
            ...state.auvConfig[action.section],
            ...action.payload
          }
        }
      }
    
    case 'UPDATE_MISSION_CONFIG':
      return {
        ...state,
        missionConfig: {
          ...state.missionConfig,
          [action.section]: {
            ...state.missionConfig[action.section],
            ...action.payload
          }
        }
      }
    
    case 'SAVE_CONFIGURATION':
      const newConfig = {
        name: action.payload.name,
        auvConfig: state.auvConfig,
        missionConfig: state.missionConfig,
        timestamp: new Date().toISOString(),
        sensors: action.payload.sensors
      }
      return {
        ...state,
        savedConfigurations: [...state.savedConfigurations, newConfig],
        currentConfigName: action.payload.name
      }
    
    case 'LOAD_CONFIGURATION':
      const config = state.savedConfigurations.find(c => c.name === action.payload)
      if (config) {
        return {
          ...state,
          auvConfig: config.auvConfig,
          missionConfig: config.missionConfig,
          currentConfigName: config.name
        }
      }
      return state
    
    case 'SET_VALIDATION_ERRORS':
      return {
        ...state,
        validationErrors: action.payload
      }
    
    case 'RESET_TO_DEFAULTS':
      return {
        ...state,
        auvConfig: { ...defaultConfig },
        missionConfig: { ...defaultMission },
        currentConfigName: '',
        validationErrors: []
      }
    
    default:
      return state
  }
}

const AUVConfigContext = createContext()

export function AUVConfigProvider({ children }) {
  const [state, dispatch] = useReducer(auvConfigReducer, initialState)
  
  const toggleConfigPanel = () => {
    dispatch({ type: 'TOGGLE_CONFIG_PANEL' })
  }
  
  const setActiveTab = (tab) => {
    dispatch({ type: 'SET_ACTIVE_TAB', payload: tab })
  }
  
  const updateAUVConfig = (section, data) => {
    dispatch({ type: 'UPDATE_AUV_CONFIG', section, payload: data })
  }
  
  const updateMissionConfig = (section, data) => {
    dispatch({ type: 'UPDATE_MISSION_CONFIG', section, payload: data })
  }
  
  const saveConfiguration = (name, sensors) => {
    dispatch({ type: 'SAVE_CONFIGURATION', payload: { name, sensors } })
  }
  
  const loadConfiguration = (name) => {
    dispatch({ type: 'LOAD_CONFIGURATION', payload: name })
  }
  
  const validateConfiguration = () => {
    const errors = []
    
    // Power budget validation
    const totalPower = parseFloat(state.auvConfig.power.consumption.total.replace('W', ''))
    const batteryCapacity = parseFloat(state.auvConfig.power.battery.capacity.replace('kWh', '')) * 1000
    const endurance = parseFloat(state.auvConfig.power.endurance.cruise.replace(' hours', ''))
    
    if (totalPower * endurance > batteryCapacity) {
      errors.push('Power consumption exceeds battery capacity for specified endurance')
    }
    
    // Weight/buoyancy validation
    const maxPayload = parseFloat(state.auvConfig.payload.maxCapacity.replace('kg', ''))
    const currentPayload = parseFloat(state.auvConfig.payload.currentWeight.replace('kg', ''))
    
    if (currentPayload > maxPayload) {
      errors.push('Current payload weight exceeds maximum capacity')
    }
    
    // Depth validation
    const maxDepth = parseFloat(state.auvConfig.operating.maxDepth.replace('m', ''))
    const abortDepth = parseFloat(state.missionConfig.safety.abortDepth.replace('m', ''))
    
    if (abortDepth <= maxDepth) {
      errors.push('Abort depth should be greater than maximum operating depth')
    }
    
    dispatch({ type: 'SET_VALIDATION_ERRORS', payload: errors })
    return errors.length === 0
  }
  
  const resetToDefaults = () => {
    dispatch({ type: 'RESET_TO_DEFAULTS' })
  }
  
  const value = {
    ...state,
    toggleConfigPanel,
    setActiveTab,
    updateAUVConfig,
    updateMissionConfig,
    saveConfiguration,
    loadConfiguration,
    validateConfiguration,
    resetToDefaults
  }
  
  return (
    <AUVConfigContext.Provider value={value}>
      {children}
    </AUVConfigContext.Provider>
  )
}

export function useAUVConfig() {
  const context = useContext(AUVConfigContext)
  if (!context) {
    throw new Error('useAUVConfig must be used within an AUVConfigProvider')
  }
  return context
}
