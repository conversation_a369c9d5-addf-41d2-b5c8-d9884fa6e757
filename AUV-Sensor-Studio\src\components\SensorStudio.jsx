import React, { useState } from 'react'
import { Viewport3D } from './Viewport3D'
import { SensorPanel } from './SensorPanel'
import { Toolbar } from './Toolbar'
import { useSensors } from '../context/SensorContext'

export function SensorStudio() {
  const [panelWidth, setPanelWidth] = useState(320)
  const [isDragging, setIsDragging] = useState(false)
  const { sensors, selectedSensor } = useSensors()

  const handleMouseDown = (e) => {
    setIsDragging(true)
    e.preventDefault()
  }

  const handleMouseMove = (e) => {
    if (!isDragging) return
    const newWidth = window.innerWidth - e.clientX
    setPanelWidth(Math.max(280, Math.min(600, newWidth)))
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging])

  return (
    <div style={{
      display: 'flex',
      height: '100vh',
      background: '#0a0a0a',
      color: '#ffffff'
    }}>
      {/* Main 3D Viewport */}
      <div style={{
        flex: 1,
        position: 'relative',
        minWidth: '400px'
      }}>
        <Toolbar />
        <Viewport3D />
        
        {/* Status Bar */}
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '32px',
          background: 'rgba(0,0,0,0.8)',
          display: 'flex',
          alignItems: 'center',
          padding: '0 1rem',
          fontSize: '0.85rem',
          borderTop: '1px solid #333'
        }}>
          <span>Sensors: {sensors.length}</span>
          {selectedSensor && (
            <span style={{ marginLeft: '2rem', color: '#4CAF50' }}>
              Selected: {sensors.find(s => s.id === selectedSensor)?.name}
            </span>
          )}
        </div>
      </div>

      {/* Resizer */}
      <div
        onMouseDown={handleMouseDown}
        style={{
          width: '4px',
          background: isDragging ? '#4CAF50' : '#333',
          cursor: 'col-resize',
          transition: isDragging ? 'none' : 'background 0.2s'
        }}
      />

      {/* Right Panel */}
      <div style={{
        width: `${panelWidth}px`,
        background: '#111',
        borderLeft: '1px solid #333',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <SensorPanel />
      </div>
    </div>
  )
}
