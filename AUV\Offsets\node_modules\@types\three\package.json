{"name": "@types/three", "version": "0.176.0", "description": "TypeScript definitions for three", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/three", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "josh<PERSON><PERSON><PERSON>", "url": "https://github.com/joshua<PERSON>s"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}], "type": "module", "main": "", "types": "index.d.ts", "exports": {".": {"import": "./build/three.module.js", "require": "./build/three.cjs"}, "./examples/fonts/*": "./examples/fonts/*", "./examples/jsm/*": "./examples/jsm/*", "./addons": "./examples/jsm/Addons.js", "./addons/*": "./examples/jsm/*", "./src/*": "./src/*", "./webgpu": "./build/three.webgpu.js", "./tsl": "./build/three.tsl.js", "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/three"}, "scripts": {}, "dependencies": {"@dimforge/rapier3d-compat": "^0.12.0", "@tweenjs/tween.js": "~23.1.3", "@types/stats.js": "*", "@types/webxr": "*", "@webgpu/types": "*", "fflate": "~0.8.2", "meshoptimizer": "~0.18.1"}, "peerDependencies": {}, "typesPublisherContentHash": "2633ecc9f78b2c3a7ff53ae098668242bacbcea8b1a3e8ca27d1bf0a943233a6", "typeScriptVersion": "5.1"}