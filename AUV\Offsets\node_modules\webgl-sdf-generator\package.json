{"name": "webgl-sdf-generator", "version": "1.1.1", "description": "WebGL-accelerated signed distance field generation for 2D paths", "main": "dist/webgl-sdf-generator.js", "module": "dist/webgl-sdf-generator.mjs", "repository": {"type": "git", "url": "https://github.com/lojjic/webgl-sdf-generator.git"}, "scripts": {"build": "rollup -c rollup.config.js", "test": "npm run build && node test/run.js"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@babel/core": "^7.16.0", "@babel/node": "^7.16.0", "@babel/plugin-transform-modules-commonjs": "^7.16.0", "@rollup/plugin-buble": "^0.21.3", "event-target-polyfill": "0.0.3", "gl": "^4.9.2", "rollup": "^2.56.3", "rollup-plugin-glsl": "^1.3.0", "rollup-plugin-terser": "^7.0.2"}, "files": ["/dist", "/LICENSE.txt", "/README.md"]}