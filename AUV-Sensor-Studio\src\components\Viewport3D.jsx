import React, { Suspense, useState } from 'react'
import { Canvas } from '@react-three/fiber'
import { OrbitControls, Grid, Html } from '@react-three/drei'
import { AUVModel } from './AUVModel'
import { SensorMarkers } from './SensorMarkers'
import { useSensors } from '../context/SensorContext'

function LoadingFallback() {
  return (
    <Html center>
      <div style={{
        padding: '1rem',
        background: 'rgba(0,0,0,0.8)',
        borderRadius: '4px',
        color: 'white'
      }}>
        Loading 3D Scene...
      </div>
    </Html>
  )
}

export function Viewport3D() {
  const [cameraPosition, setCameraPosition] = useState([3, 2, 3])
  const { sensors } = useSensors()

  return (
    <div style={{
      width: '100%',
      height: '100%',
      background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)'
    }}>
      <Canvas
        camera={{
          position: cameraPosition,
          fov: 50,
          near: 0.1,
          far: 1000
        }}
        style={{ width: '100%', height: '100%' }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.4} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
        <pointLight position={[-10, -10, -5]} intensity={0.3} />

        {/* Scene */}
        <Suspense fallback={<LoadingFallback />}>
          {/* Grid */}
          <Grid
            args={[10, 10]}
            cellSize={0.5}
            cellThickness={0.5}
            cellColor="#333"
            sectionSize={2}
            sectionThickness={1}
            sectionColor="#555"
            fadeDistance={20}
            fadeStrength={1}
            followCamera={false}
            infiniteGrid={true}
          />

          {/* Coordinate System Axes */}
          <group>
            {/* X-axis (Starboard) - Red */}
            <mesh position={[0.5, 0, 0]} rotation={[0, 0, -Math.PI / 2]}>
              <cylinderGeometry args={[0.01, 0.01, 1]} />
              <meshBasicMaterial color="#ff4444" />
            </mesh>

            {/* Y-axis (Forward) - Green */}
            <mesh position={[0, 0.5, 0]}>
              <cylinderGeometry args={[0.01, 0.01, 1]} />
              <meshBasicMaterial color="#44ff44" />
            </mesh>

            {/* Z-axis (Down) - Blue */}
            <mesh position={[0, 0, 0.5]} rotation={[Math.PI / 2, 0, 0]}>
              <cylinderGeometry args={[0.01, 0.01, 1]} />
              <meshBasicMaterial color="#4444ff" />
            </mesh>

            {/* Axis Labels */}
            <Html position={[1, 0, 0]} center>
              <div style={{
                color: '#ff4444',
                fontSize: '0.8rem',
                fontWeight: 'bold',
                background: 'rgba(0,0,0,0.7)',
                padding: '0.2rem 0.4rem',
                borderRadius: '3px'
              }}>
                +X (Starboard)
              </div>
            </Html>

            <Html position={[0, 1, 0]} center>
              <div style={{
                color: '#44ff44',
                fontSize: '0.8rem',
                fontWeight: 'bold',
                background: 'rgba(0,0,0,0.7)',
                padding: '0.2rem 0.4rem',
                borderRadius: '3px'
              }}>
                +Y (Forward)
              </div>
            </Html>

            <Html position={[0, 0, 1]} center>
              <div style={{
                color: '#4444ff',
                fontSize: '0.8rem',
                fontWeight: 'bold',
                background: 'rgba(0,0,0,0.7)',
                padding: '0.2rem 0.4rem',
                borderRadius: '3px'
              }}>
                +Z (Down)
              </div>
            </Html>
          </group>

          {/* AUV Model */}
          <AUVModel />

          {/* Sensor Markers */}
          <SensorMarkers sensors={sensors} />
        </Suspense>

        {/* Controls */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={1}
          maxDistance={20}
          target={[0, 0, 0]}
        />
      </Canvas>

      {/* 3D Controls Help */}
      <div style={{
        position: 'absolute',
        bottom: '40px',
        left: '1rem',
        background: 'rgba(0,0,0,0.7)',
        padding: '0.5rem',
        borderRadius: '4px',
        fontSize: '0.8rem',
        color: '#ccc'
      }}>
        <div>Left Click + Drag: Rotate</div>
        <div>Right Click + Drag: Pan</div>
        <div>Scroll: Zoom</div>
      </div>
    </div>
  )
}
