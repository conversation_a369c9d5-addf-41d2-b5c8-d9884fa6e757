{"version": 3, "file": "TechnicolorShader.cjs", "sources": ["../../src/shaders/TechnicolorShader.ts"], "sourcesContent": ["/**\n * Technicolor Shader\n * Simulates the look of the two-strip technicolor process popular in early 20th century films.\n * More historical info here: http://www.widescreenmuseum.com/oldcolor/technicolor1.htm\n * Demo here: http://charliehoey.com/technicolor_shader/shader_test.html\n */\n\nexport const TechnicolorShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 tex = texture2D( tDiffuse, vec2( vUv.x, vUv.y ) );\n    \tvec4 newTex = vec4(tex.r, (tex.g + tex.b) * .5, (tex.g + tex.b) * .5, 1.0);\n\n    \tgl_FragColor = newTex;\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;AAOO,MAAM,oBAAoB;AAAA,EAC/B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,EAC1B;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAa7B;;"}