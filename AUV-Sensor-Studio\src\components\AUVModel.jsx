import React, { useRef, useState, useEffect } from 'react'
import { useFrame } from '@react-three/fiber'
import { useGLTF } from '@react-three/drei'
import * as THREE from 'three'
import { useSensors } from '../context/SensorContext'

// Hugin 3000 AUV Model - Larger, long-range AUV
function Hugin3000Model() {
  const meshRef = useRef()

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={meshRef}>
      {/* Main hull - larger for Hugin 3000 */}
      <mesh position={[0, 0, 0]} rotation={[0, 0, Math.PI / 2]} castShadow receiveShadow>
        <capsuleGeometry args={[0.2, 2.0, 4, 8]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>

      {/* Nose cone - more pronounced for <PERSON>gin 3000 */}
      <mesh position={[0, 1.2, 0]} rotation={[0, 0, Math.PI / 2]} castShadow>
        <coneGeometry args={[0.2, 0.4, 8]} />
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* Fins - larger for Hugin 3000 */}
      {[0, 1, 2, 3].map(i => (
        <mesh
          key={i}
          position={[0, -0.8, 0]}
          rotation={[(i * Math.PI) / 2, 0, 0]}
          castShadow
        >
          <boxGeometry args={[0.03, 0.4, 0.5]} />
          <meshStandardMaterial
            color="#2a2a2a"
            metalness={0.7}
            roughness={0.3}
          />
        </mesh>
      ))}

      {/* Propeller - larger for Hugin 3000 */}
      <mesh position={[0, -1.2, 0]} rotation={[Math.PI / 2, 0, 0]} castShadow>
        <cylinderGeometry args={[0.08, 0.08, 0.15]} />
        <meshStandardMaterial
          color="#333"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* Additional equipment pods for Hugin 3000 */}
      <mesh position={[0.15, 0.3, 0]} castShadow>
        <boxGeometry args={[0.08, 0.3, 0.08]} />
        <meshStandardMaterial color="#444" metalness={0.6} roughness={0.4} />
      </mesh>
      <mesh position={[-0.15, 0.3, 0]} castShadow>
        <boxGeometry args={[0.08, 0.3, 0.08]} />
        <meshStandardMaterial color="#444" metalness={0.6} roughness={0.4} />
      </mesh>
    </group>
  )
}

// Hugin 1000 AUV Model - Smaller, more compact design
function Hugin1000Model() {
  const meshRef = useRef()

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={meshRef}>
      {/* Main hull - smaller and more compact for Hugin 1000 */}
      <mesh position={[0, 0, 0]} rotation={[0, 0, Math.PI / 2]} castShadow receiveShadow>
        <capsuleGeometry args={[0.12, 1.2, 4, 8]} />
        <meshStandardMaterial
          color="#3a3a3a"
          metalness={0.6}
          roughness={0.4}
        />
      </mesh>

      {/* Nose cone - smaller for Hugin 1000 */}
      <mesh position={[0, 0.7, 0]} rotation={[0, 0, Math.PI / 2]} castShadow>
        <coneGeometry args={[0.12, 0.25, 8]} />
        <meshStandardMaterial
          color="#2a2a2a"
          metalness={0.7}
          roughness={0.3}
        />
      </mesh>

      {/* Fins - smaller for Hugin 1000 */}
      {[0, 1, 2, 3].map(i => (
        <mesh
          key={i}
          position={[0, -0.5, 0]}
          rotation={[(i * Math.PI) / 2, 0, 0]}
          castShadow
        >
          <boxGeometry args={[0.02, 0.25, 0.3]} />
          <meshStandardMaterial
            color="#444"
            metalness={0.5}
            roughness={0.5}
          />
        </mesh>
      ))}

      {/* Propeller - smaller for Hugin 1000 */}
      <mesh position={[0, -0.7, 0]} rotation={[Math.PI / 2, 0, 0]} castShadow>
        <cylinderGeometry args={[0.04, 0.04, 0.08]} />
        <meshStandardMaterial
          color="#555"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>

      {/* Single equipment pod for Hugin 1000 */}
      <mesh position={[0, 0.2, -0.1]} castShadow>
        <boxGeometry args={[0.06, 0.2, 0.06]} />
        <meshStandardMaterial color="#555" metalness={0.5} roughness={0.5} />
      </mesh>
    </group>
  )
}

// Component to load GLB models (auto-loaded or custom)
function GLBModel({ url, fallbackVehicle }) {
  const { setModelLoaded } = useSensors()
  const [loadError, setLoadError] = useState(false)

  try {
    const { scene } = useGLTF(url)

    useEffect(() => {
      setModelLoaded(true)
      setLoadError(false)
      return () => setModelLoaded(false)
    }, [setModelLoaded])

    return <primitive object={scene} scale={0.5} />
  } catch (error) {
    console.warn('Failed to load GLB model:', error)

    useEffect(() => {
      setLoadError(true)
      setModelLoaded(false)
    }, [setModelLoaded])

    // Fallback to procedural model
    return fallbackVehicle === 'hugin1000' ? <Hugin1000Model /> : <Hugin3000Model />
  }
}

// Auto-loading model component that tries to load vehicle-specific GLB
function AutoLoadModel({ vehicle }) {
  const modelPath = `/models/${vehicle}.glb`
  return <GLBModel url={modelPath} fallbackVehicle={vehicle} />
}

export function AUVModel() {
  const { customModelUrl, modelType, selectedVehicle } = useSensors()

  // Function to render the appropriate vehicle model
  const renderVehicleModel = () => {
    // Custom GLB model takes priority
    if (customModelUrl && modelType === 'custom') {
      return <GLBModel url={customModelUrl} fallbackVehicle={selectedVehicle} />
    }

    // Auto-load vehicle-specific GLB model
    if (modelType === 'auto') {
      return <AutoLoadModel vehicle={selectedVehicle} />
    }

    // Fallback to procedural models
    return selectedVehicle === 'hugin1000' ? <Hugin1000Model /> : <Hugin3000Model />
  }

  return (
    <group position={[0, 0, 0]}>
      {renderVehicleModel()}
    </group>
  )
}
