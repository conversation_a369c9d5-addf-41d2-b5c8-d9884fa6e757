import React, { useRef, useState, useEffect } from 'react'
import { useFrame } from '@react-three/fiber'
import { useGLTF } from '@react-three/drei'
import * as THREE from 'three'
import { useSensors } from '../context/SensorContext'

// Hugin 3000 AUV Model - Larger, long-range AUV
function Hugin3000Model() {
  const meshRef = useRef()

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={meshRef}>
      {/* Main hull - larger for Hugin 3000 */}
      <mesh position={[0, 0, 0]} rotation={[0, 0, Math.PI / 2]} castShadow receiveShadow>
        <capsuleGeometry args={[0.2, 2.0, 4, 8]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>

      {/* Nose cone - more pronounced for <PERSON>gin 3000 */}
      <mesh position={[0, 1.2, 0]} rotation={[0, 0, Math.PI / 2]} castShadow>
        <coneGeometry args={[0.2, 0.4, 8]} />
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* Fins - larger for Hugin 3000 */}
      {[0, 1, 2, 3].map(i => (
        <mesh
          key={i}
          position={[0, -0.8, 0]}
          rotation={[(i * Math.PI) / 2, 0, 0]}
          castShadow
        >
          <boxGeometry args={[0.03, 0.4, 0.5]} />
          <meshStandardMaterial
            color="#2a2a2a"
            metalness={0.7}
            roughness={0.3}
          />
        </mesh>
      ))}

      {/* Propeller - larger for Hugin 3000 */}
      <mesh position={[0, -1.2, 0]} rotation={[Math.PI / 2, 0, 0]} castShadow>
        <cylinderGeometry args={[0.08, 0.08, 0.15]} />
        <meshStandardMaterial
          color="#333"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* Additional equipment pods for Hugin 3000 */}
      <mesh position={[0.15, 0.3, 0]} castShadow>
        <boxGeometry args={[0.08, 0.3, 0.08]} />
        <meshStandardMaterial color="#444" metalness={0.6} roughness={0.4} />
      </mesh>
      <mesh position={[-0.15, 0.3, 0]} castShadow>
        <boxGeometry args={[0.08, 0.3, 0.08]} />
        <meshStandardMaterial color="#444" metalness={0.6} roughness={0.4} />
      </mesh>
    </group>
  )
}

// Hugin 1000 AUV Model - Smaller, more compact design
function Hugin1000Model() {
  const meshRef = useRef()

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={meshRef}>
      {/* Main hull - smaller and more compact for Hugin 1000 */}
      <mesh position={[0, 0, 0]} rotation={[0, 0, Math.PI / 2]} castShadow receiveShadow>
        <capsuleGeometry args={[0.12, 1.2, 4, 8]} />
        <meshStandardMaterial
          color="#3a3a3a"
          metalness={0.6}
          roughness={0.4}
        />
      </mesh>

      {/* Nose cone - smaller for Hugin 1000 */}
      <mesh position={[0, 0.7, 0]} rotation={[0, 0, Math.PI / 2]} castShadow>
        <coneGeometry args={[0.12, 0.25, 8]} />
        <meshStandardMaterial
          color="#2a2a2a"
          metalness={0.7}
          roughness={0.3}
        />
      </mesh>

      {/* Fins - smaller for Hugin 1000 */}
      {[0, 1, 2, 3].map(i => (
        <mesh
          key={i}
          position={[0, -0.5, 0]}
          rotation={[(i * Math.PI) / 2, 0, 0]}
          castShadow
        >
          <boxGeometry args={[0.02, 0.25, 0.3]} />
          <meshStandardMaterial
            color="#444"
            metalness={0.5}
            roughness={0.5}
          />
        </mesh>
      ))}

      {/* Propeller - smaller for Hugin 1000 */}
      <mesh position={[0, -0.7, 0]} rotation={[Math.PI / 2, 0, 0]} castShadow>
        <cylinderGeometry args={[0.04, 0.04, 0.08]} />
        <meshStandardMaterial
          color="#555"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>

      {/* Single equipment pod for Hugin 1000 */}
      <mesh position={[0, 0.2, -0.1]} castShadow>
        <boxGeometry args={[0.06, 0.2, 0.06]} />
        <meshStandardMaterial color="#555" metalness={0.5} roughness={0.5} />
      </mesh>
    </group>
  )
}

// Component to render parsed STEP data
function STEPModel({ url }) {
  const { setModelLoaded } = useSensors()
  const [geometry, setGeometry] = useState(null)

  useEffect(() => {
    fetch(url)
      .then(response => response.text())
      .then(data => {
        const stepData = JSON.parse(data)
        setGeometry(stepData.geometry)
        setModelLoaded(true)
      })
      .catch(error => {
        console.error('Failed to load STEP data:', error)
        setModelLoaded(false)
      })

    return () => setModelLoaded(false)
  }, [url, setModelLoaded])

  if (!geometry) return <Hugin3000Model />

  return (
    <group>
      {/* Render points as small spheres */}
      {geometry.points.map((point, index) => (
        <mesh key={index} position={point}>
          <sphereGeometry args={[0.01, 8, 8]} />
          <meshBasicMaterial color="#00ff00" />
        </mesh>
      ))}

      {/* Show bounding box */}
      {geometry.boundingBox && (
        <lineSegments>
          <edgesGeometry args={[new THREE.BoxGeometry(
            geometry.boundingBox.max[0] - geometry.boundingBox.min[0],
            geometry.boundingBox.max[1] - geometry.boundingBox.min[1],
            geometry.boundingBox.max[2] - geometry.boundingBox.min[2]
          )]} />
          <lineBasicMaterial color="#ffff00" />
        </lineSegments>
      )}
    </group>
  )
}

// Component to load external 3D models
function ExternalModel({ url }) {
  const { setModelLoaded } = useSensors()

  try {
    const { scene } = useGLTF(url)

    useEffect(() => {
      setModelLoaded(true)
      return () => setModelLoaded(false)
    }, [setModelLoaded])

    return <primitive object={scene} scale={0.5} />
  } catch (error) {
    console.warn('Failed to load external model:', error)
    return <Hugin3000Model />
  }
}

export function AUVModel() {
  const { customModelUrl, modelType, selectedVehicle } = useSensors()

  // Function to render the appropriate vehicle model
  const renderVehicleModel = () => {
    if (customModelUrl && modelType === 'step-parsed') {
      return <STEPModel url={customModelUrl} />
    }
    if (customModelUrl && modelType !== 'fallback') {
      return <ExternalModel url={customModelUrl} />
    }

    // Render selected vehicle model
    switch (selectedVehicle) {
      case 'hugin1000':
        return <Hugin1000Model />
      case 'hugin3000':
      default:
        return <Hugin3000Model />
    }
  }

  return (
    <group position={[0, 0, 0]}>
      {renderVehicleModel()}
    </group>
  )
}
