import React, { useRef, useState, useEffect } from 'react'
import { useFrame } from '@react-three/fiber'
import { useGLTF } from '@react-three/drei'
import { useSensors } from '../context/SensorContext'

// Fallback AUV shape when no model is loaded
function FallbackAUV() {
  const meshRef = useRef()
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={meshRef}>
      {/* Main hull - oriented with Y+ forward */}
      <mesh position={[0, 0, 0]} rotation={[0, 0, Math.PI / 2]} castShadow receiveShadow>
        <capsuleGeometry args={[0.15, 1.5, 4, 8]} />
        <meshStandardMaterial
          color="#2a2a2a"
          metalness={0.7}
          roughness={0.3}
        />
      </mesh>

      {/* Nose cone - pointing forward (Y+) */}
      <mesh position={[0, 0.9, 0]} rotation={[0, 0, Math.PI / 2]} castShadow>
        <coneGeometry args={[0.15, 0.3, 8]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.8}
          roughness={0.2}
        />
      </mesh>
      
      {/* Fins - positioned for Y+ forward orientation */}
      {[0, 1, 2, 3].map(i => (
        <mesh
          key={i}
          position={[0, -0.6, 0]}
          rotation={[(i * Math.PI) / 2, 0, 0]}
          castShadow
        >
          <boxGeometry args={[0.02, 0.3, 0.4]} />
          <meshStandardMaterial
            color="#333"
            metalness={0.6}
            roughness={0.4}
          />
        </mesh>
      ))}

      {/* Propeller - at rear (Y-) */}
      <mesh position={[0, -0.9, 0]} rotation={[Math.PI / 2, 0, 0]} castShadow>
        <cylinderGeometry args={[0.05, 0.05, 0.1]} />
        <meshStandardMaterial
          color="#444"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>
    </group>
  )
}

// Component to load external 3D models
function ExternalModel({ url }) {
  const { setModelLoaded } = useSensors()
  
  try {
    const { scene } = useGLTF(url)
    
    useEffect(() => {
      setModelLoaded(true)
      return () => setModelLoaded(false)
    }, [setModelLoaded])
    
    return <primitive object={scene} scale={0.5} />
  } catch (error) {
    console.warn('Failed to load external model:', error)
    return <FallbackAUV />
  }
}

export function AUVModel() {
  const { customModelUrl, modelType } = useSensors()

  return (
    <group position={[0, 0, 0]}>
      {customModelUrl && modelType !== 'fallback' ? (
        <ExternalModel url={customModelUrl} />
      ) : (
        <FallbackAUV />
      )}
    </group>
  )
}
