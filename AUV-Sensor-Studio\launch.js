#!/usr/bin/env node

const { exec, spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 AUV Sensor Studio Launcher')
console.log('=============================\n')

// Check if we're in the right directory
const checkProjectSetup = () => {
  const requiredFiles = ['package.json', 'vite.config.js', 'src/App.jsx']
  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file))
  
  if (missingFiles.length > 0) {
    console.error('❌ Missing required files:', missingFiles.join(', '))
    return false
  }
  
  console.log('✅ Project structure verified')
  return true
}

// Check for node_modules and critical dependencies
const checkDependencies = () => {
  const nodeModulesPath = path.join(process.cwd(), 'node_modules')
  
  if (!fs.existsSync(nodeModulesPath)) {
    console.warn('⚠️  Dependencies not installed')
    return false
  }
  
  const criticalDeps = ['react', 'three', '@react-three/fiber']
  const missingDeps = criticalDeps.filter(dep => 
    !fs.existsSync(path.join(nodeModulesPath, dep))
  )
  
  if (missingDeps.length > 0) {
    console.warn('⚠️  Missing dependencies:', missingDeps.join(', '))
    return false
  }
  
  console.log('✅ Dependencies verified')
  return true
}

// Determine package manager
const getPackageManager = () => {
  if (fs.existsSync('pnpm-lock.yaml')) return 'pnpm'
  if (fs.existsSync('yarn.lock')) return 'yarn'
  return 'npm'
}

// Install dependencies
const installDependencies = (packageManager) => {
  console.log('📦 Installing dependencies...')
  
  return new Promise((resolve, reject) => {
    const commands = {
      npm: 'npm install',
      yarn: 'yarn',
      pnpm: 'pnpm install'
    }
    
    const child = exec(commands[packageManager], (error) => {
      if (error) {
        console.error('❌ Installation failed:', error.message)
        reject(error)
        return
      }
      console.log('✅ Dependencies installed')
      resolve()
    })
    
    child.stdout.pipe(process.stdout)
    child.stderr.pipe(process.stderr)
  })
}

// Launch the application
const launchApp = async () => {
  try {
    // Verify project setup
    if (!checkProjectSetup()) {
      console.error('❌ Project setup invalid')
      return
    }
    
    // Check dependencies
    const depsInstalled = checkDependencies()
    const packageManager = getPackageManager()
    
    console.log(`📦 Using ${packageManager} package manager`)
    
    // Install if needed
    if (!depsInstalled) {
      await installDependencies(packageManager)
    }
    
    console.log('🚀 Starting AUV Sensor Studio...')
    console.log('📱 Opening at http://localhost:3001')
    console.log('🛑 Press Ctrl+C to stop\n')
    
    // Launch dev server
    const commands = {
      npm: ['run', 'dev'],
      yarn: ['dev'],
      pnpm: ['dev']
    }
    
    const child = spawn(packageManager, commands[packageManager], {
      stdio: 'inherit',
      shell: true
    })
    
    child.on('error', (error) => {
      console.error('❌ Failed to start:', error.message)
    })
    
    child.on('close', (code) => {
      if (code !== 0) {
        console.error(`❌ Process exited with code ${code}`)
      } else {
        console.log('👋 AUV Sensor Studio stopped')
      }
    })
    
  } catch (error) {
    console.error('❌ Launch failed:', error.message)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down...')
  process.exit(0)
})

// Run launcher
launchApp()
