["5", ["arm mali-g31", "31", "arm mali-g31", 0, [[1920, 1080, 7, "mecool km9 pro"]]], ["arm mali-g51", "51", "arm mali-g51", 0, [[1920, 636, 10, "telechips tcc803x_lcn"], [1920, 1080, 5, "skyworth 8n10 g1a"], [2224, 1080, 19, "hua<PERSON> honor 9x"]]], ["arm mali-g52", "52", "arm mali-g52", 0, [[1448, 720, 29, "samsung galaxy a21s"], [1473, 720, 16, "samsung sm-a127f"], [1554, 720, 18, "xiaomi redmi c3qp"]]], ["arm mali-g52 mc1", "52", "arm mali-g52 mc1", 0, [[1280, 736, 25, "amazon fire hd 8 (kfonwi, 2020)"]]], ["arm mali-g52 mc2", "52", "arm mali-g52 mc2", 0, [[1543, 688, 43, "ulefone power armor 14 pro"], [2110, 1080, 23, "xiaomi <PERSON>mi 10x 4g"], [2195, 1080, 19, "samsung galaxy a31 (sm-a315n)"], [2208, 1080, 22, "infinix hot 11s (x6812b)"], [2264, 1080, 20, "xiaomi redmi 9 m2004j19c"], [2400, 1080, 20, "huawei y9a frl-l22"]]], ["arm mali-g57", "57", "arm mali-g57", 0, [[2256, 1080, 49, "unisoc ums9620"]]], ["arm mali-g57 mc2", "57", "arm mali-g57 mc2", 0, [[2174, 1080, 39, "poco m4 pro 5g (21091116ag)"], [2177, 1080, 36, "xiaomi poco m4 pro"], [2208, 1080, 35, "samsung galaxy a22 5g"]]], ["arm mali-g57 mc3", "57", "arm mali-g57 mc3", 0, [[2158, 1080, 48, "realme rmx2173"]]], ["arm mali-g610 mc6", "610", "arm mali-g610 mc6", 0, [[2316, 1080, 60, "realme gt neo 3 150w"]]], ["arm mali-g68", "68", "arm mali-g68", 0, [[2207, 1080, 57, "samsung galaxy m33"]]], ["arm mali-g68 mc4", "68", "arm mali-g68 mc4", 0, [[2153, 1080, 56, "oppo reno6 5g"], [2944, 1712, 31, "lenovo tab p12"]]], ["arm mali-g71", "71", "arm mali-g71", 0, [[1280, 720, 14, "samsung sm-j337"], [1812, 1080, 53, "huawei mate 9 (mha-xxx)"], [1920, 1080, 12, "sony amai vp9 (mali-g71)"], [2009, 1080, 11, "tcl 6062"], [2016, 1080, 11, "gionee s11"], [2038, 1080, 10, "ulefone power 3"], [2076, 1080, 15, "samsung galaxy a8 2018 (sm-a530x)"], [2094, 1080, 15, "samsung sm-a730x"], [2160, 1080, 9, "oukitel k6"], [2368, 1440, 35, "huawei honor v9 (duk-xxx)"], [2560, 1440, 32, "huawei mate 9 pro (lon-xxx)"], [2678, 1440, 41, "samsung galaxy s8 (mali-g71, sm-g950x)"], [2960, 1440, 39, "samsung galaxy s8+ (mali-g71, sm-g955x)"]]], ["arm mali-g715-<PERSON><PERSON> mc11", "715", "arm mali-g715-<PERSON><PERSON> mc11", 0, [[2311, 1080, 121, "vivo x90"]]], ["arm mali-g72", "72", "arm mali-g72", 0, [[2041, 1080, 55, "huawei mate 10 pro (bla-xxx)"], [2094, 1080, 57, "samsung galaxy note 9"], [2160, 1080, 56, "huawei honor view 10 (v10, bkl-xxx)"], [2186, 1080, 24, "samsung galaxy a51 (sm-a515f)"], [2218, 1080, 21, "motorola one vision"], [2560, 1440, 43, "huawei mate 10 (alp-xxx)"], [2768, 1440, 47, "samsung galaxy s9 (mali-g72, sm-g960)"], [2792, 1440, 47, "samsung galaxy s9+ (mali-g72, sm-g965)"]]], ["arm mali-g72 mp3", "72", "arm mali-g72 mp3", 0, [[1465, 678, 39, "motorola one macro"]]], ["arm mali-g720-<PERSON><PERSON> mc12", "720", "arm mali-g720-<PERSON><PERSON> mc12", 0, [[2680, 1260, 120, "vivo x100"]]], ["arm mali-g76", "76", "arm mali-g76", 0, [[2020, 1080, 60, "samsung galaxy s10e (mali-g76, sm-g970x)"], [2029, 1080, 31, "samsung galaxy s10 plus sm-g975n"], [2047, 1080, 60, "samsung sm-g975f"], [2064, 1080, 60, "samsung galaxy note 10 5g (mali-g76, sm-n971x)"], [2111, 1080, 46, "huawei nova 5t (yal-l21)"], [2159, 1080, 47, "samsung galaxy a51 5g"], [2176, 1080, 54, "samsung galaxy s10 5g (sm-g977n)"], [2232, 1080, 59, "huawei honor 20 pro yal-l41"], [2265, 1080, 59, "huawei p30 pro"], [2328, 1128, 60, "huawei mate 30 pro (lio-lx9, lio-xl00"], [2723, 1440, 57, "samsung galaxy s10 (mali-g76, sm-g973x)"], [2730, 1440, 56, "samsung galaxy s10+ (mali-g76, sm-g975x)"], [2733, 1440, 56, "samsung galaxy s10 5g (mali-g76, sm-g977x)"], [2759, 1440, 59, "samsung galaxy note 10+ (mali-g76, sm-n975x)"], [2861, 1440, 48, "h<PERSON><PERSON> mate 20 pro"]]], ["arm mali-g76 mc4", "76", "arm mali-g76 mc4", 0, [[2134, 1080, 45, "xia<PERSON> redmi note 8 pro"]]], ["arm mali-g77", "77", "arm mali-g77", 0, [[2168, 1080, 93, "samsung galaxy s20 fe (sm-g780f)"], [2173, 1080, 58, "samsung galaxy note 20 (sm-n980f)"], [2178, 1080, 65, "samsung galaxy s20 5g (sm-g981b)"], [2200, 1080, 113, "samsung galaxy s20 ultra 5g (sm-g988b)"], [2327, 1038, 57, "samsung galaxy s20+ 5g (sm-g986b)"], [2304, 1080, 55, "oppo pdcm00"]]], ["arm mali-g77 mc9", "77", "arm mali-g77 mc9", 0, [[2161, 1080, 69, "oneplus nord 2 5g"], [2293, 1080, 59, "oppo pdsm00"], [2304, 1080, 102, "xiaomi poco x3 gt (21061110ag)"]]], ["arm mali-g78", "78", "arm mali-g78", 0, [[2272, 1017, 90, "google pixel 6"], [2176, 1080, 114, "samsung galaxy s21 5g (sm-g991b)"], [2646, 1288, 90, "<PERSON><PERSON><PERSON> mate 40 pro 5g"]]], ["arm mali-t604 mp4", "604", "arm mali-t604 mp4", 0, [[2560, 1504, 4, "google nexus 10"]]], ["arm mali-t622", "622", "arm mali-t622", 0, [[1024, 564, 12, "telechips tcc896x (quad core, development board)"], [1280, 720, 7, "leadcore l1860 (mali-t622, development board)"]]], ["arm mali-t624", "624", "arm mali-t624", 0, [[1794, 1080, 7, "huawei grace"], [1812, 1080, 6, "huawei abc-ul00"], [1830, 1080, 10, "huawei p8 max (dav-70x)"], [1920, 1080, 4, "sony amai vp9"], [1920, 1104, 9, "huawei dtab compact d-02h (docomo)"], [1920, 1128, 8, "huawei dtab d-01h (docomo)"], [1824, 1200, 9, "huawei mediapad m2 (m2-80xx)"], [1830, 1200, 9, "huawei mediapad x2 (gem-701l, gem-702l, gem-703l)"]]], ["arm mali-t624 mp2", "624", "arm mali-t624 mp2", 0, [[1280, 720, 5, "samsung sm-g910f (mali-t624)"]]], ["arm mali-t624 mp4", "624", "arm mali-t624 mp4", 0, [[1794, 1080, 9, "huawei z100"]]], ["arm mali-t628", "628", "arm mali-t628", 0, [[1024, 600, 30, "thinkware in<PERSON> davinci"], [2560, 1536, 8, "meizu mx4 pro"]]], ["arm mali-t628 mp2", "628", "arm mali-t628 mp2", 0, [[1280, 720, 8, "leadcore l1860 (development board)"]]], ["arm mali-t628 mp6", "628", "arm mali-t628 mp6", 0, [[800, 480, 35, "gen2wave rp1600"], [1280, 672, 14, "hardkernel odroid-xu3 (development board)"], [1280, 720, 26, "samsung galaxy alpha (mali-t628, sm-g850)"], [1920, 1080, 11, "samsung galaxy note iii (mali-t628, sm-n900, sm-n9000q)"], [2560, 1600, 3, "samsung sm-t520 galaxy tab 10.1"]]], ["arm mali-t720", "720", "arm mali-t720", 0, [[432, 240, 16, "unihertz jelly pro"], [782, 480, 8, "mobiistar lai zoro"], [784, 480, 8, "lg k3 (k100)"], [791, 480, 6, "i-mobile i-style 812 4g"], [800, 480, 4, "lava iris 550"], [854, 480, 3, "verykool sl5009 jet"], [897, 540, 5, "bluboo xfire"], [960, 540, 3, "siswoo a5 chocolate"], [1024, 552, 4, "bb-mobile tq763i techno 7.0 lte"], [1024, 714, 9, "bluedot bnt-791 (2g)"], [1024, 720, 3, "zte e8qp"], [1280, 624, 10, "panasonic p55 novo 4g"], [1280, 648, 4, "tcl 9025"], [1280, 656, 7, "acer a1-734 iconia talk s"], [1184, 720, 4, "lenovo xt1700, xt1706, k10a40"], [1187, 720, 7, "lg k8 (k350)"], [1189, 720, 5, "ark impulse p2"], [1193, 720, 7, "lg x power (k220, f750)"], [1196, 720, 2, "dtac phone m2"], [1198, 720, 5, "dtac phone t3"], [1205, 720, 7, "firefly aurii passion"], [1206, 720, 4, "archos 50 power"], [1208, 720, 3, "advan i7 plus"], [1217, 720, 7, "energy sistem energy phone max 2+"], [1238, 720, 9, "vnpt technology vivas lotus s3 lte"], [1280, 720, 2, "archos bush spira c2 5"], [1280, 736, 2, "digma cs1062ml citi 1903 4g"], [1280, 737, 3, "waywalkers t805g"], [1280, 740, 3, "casper via l8"], [1280, 746, 3, "philips tle821l e line 4g"], [1280, 752, 3, "4good light at200"], [1280, 755, 7, "leotec letab1020 supernova qi32"], [1356, 720, 8, "xiaolajiao la-v11"], [1360, 720, 7, "tecno in5"], [1368, 720, 7, "tinno p100"], [1280, 800, 4, "samsung galaxy tab e 8.0 (sm-t375x, sm-t377x)"], [1920, 936, 4, "panasonic eluga note"], [1920, 996, 4, "cube technology u83 iplay10"], [1776, 1080, 4, "fly fs522 cirrus 14"], [1787, 1080, 5, "lg x cam (k580, f690)"], [1920, 1008, 6, "alcatel one touch xess (p17aa)"], [1794, 1080, 3, "ramos mos 1 max"], [1800, 1080, 4, "archos sense 55 s"], [1815, 1080, 4, "archos diamond plus"], [1920, 1032, 5, "virgin media tellytablet"], [1920, 1080, 2, "infocus m640"], [1920, 1104, 4, "vestel v tab 7030"], [1920, 1116, 4, "jty q101"], [1920, 1128, 2, "archos 80 oxygen"], [2009, 1080, 4, "tcl 5099"], [1920, 1136, 4, "asus zenpad 10 (p028 z301m)"], [2712, 1440, 45, "lenovo tab 2 501lv (softbank)"]]], ["arm mali-t760", "760", "arm mali-t760", 0, [[854, 480, 14, "aux t6200l"], [897, 540, 13, "sony xperia e4g (e20xx)"], [960, 540, 12, "gionee v381"], [960, 568, 24, "asus c100pa chromebook flip"], [1024, 552, 12, "archos 70 helium"], [1024, 720, 10, "wiz t-8168"], [1188, 720, 9, "lg h520 magna, h522 prime plus"], [1196, 720, 9, "acer s57 liquid jade z"], [1280, 720, 8, "dunetek vitamin a"], [1280, 736, 8, "archos 80b helium"], [1280, 752, 8, "frael m10g 4g"], [1280, 768, 8, "meizu m1"], [1794, 1080, 6, "sugar 2 ss136 l8560"], [1920, 1032, 9, "qbic bxp-300 box pc"], [1920, 1080, 4, "byxpress mphone xone"], [1920, 1104, 6, "cube technology t7"], [1920, 1128, 5, "nec lavietab pc-te510bal"], [2048, 1440, 3, "teclast p98 4g"], [2560, 1440, 10, "samsung galaxy note 4 (mali-t760, sm-n910x, sm-n916)"]]], ["arm mali-t760 mp6", "760", "arm mali-t760 mp6", 0, [[1920, 1080, 17, "samsung galaxy a8 (mali-t760, sm-a800x, scv32)"], [2048, 1536, 12, "samsung galaxy tab s 2 8.0 (sm-t710, sm-t715)"], [2560, 1532, 10, "samsung galaxy note edge (mali-t760, sm-n915x)"], [2560, 1600, 10, "samsung galaxy tab s 10.5 (mali-t760, sm-t805s)"]]], ["arm mali-t760 mp8", "760", "arm mali-t760 mp8", 0, [[1280, 768, 42, "samsung sm-w2016"], [1920, 1080, 25, "meizu niux"], [2160, 1200, 17, "idealens k2"], [2560, 1440, 12, "le xiang deepoon m2 vr"], [2560, 1504, 15, "bungbungame kalos 2"]]], ["arm mali-t764", "764", "arm mali-t764", 0, [[1024, 600, 19, "gpd q9"], [1280, 720, 15, "gpd xd"], [1280, 752, 13, "kruger & matz 1064.1g eagle"], [1280, 800, 13, "pipo p7"], [1920, 1008, 6, "rockchip mk809 4k tv stick"], [1920, 1010, 8, "pipo p7 hd"], [1920, 1020, 5, "rockchip mk903v mini tv"], [1920, 1032, 7, "acooo oneboard pro+"], [1872, 1080, 7, "contextmedia wallboard 32 tablet (p-wal-106-yit-01)"], [1920, 1080, 7, "rockchip cs4k tv box"], [1920, 1128, 7, "archos 101 oxygen"], [2048, 1437, 5, "haier pad 971"], [2048, 1440, 5, "hisense f5281 vidaa pad"], [2560, 1504, 4, "teclast p90hd"]]], ["arm mali-t820", "820", "arm mali-t820", 0, [[1344, 720, 5, "lenovo k320t"], [1776, 1080, 6, "leagoo t5c"], [1920, 1008, 7, "probox2 ava tv box"], [1920, 1080, 4, "skyworth coocaa 5s32 n2"]]], ["arm mali-t830", "830", "arm mali-t830", 0, [[1280, 720, 9, "samsung galaxy on7 (mali-t830, sm-g600x)"], [1280, 800, 9, "samsung sm-t230nw (mali-t830)"], [1776, 1080, 9, "huawei p10 lite (was-xxx)"], [1794, 1080, 9, "huawei honor 6x (bln-xxx)"], [1920, 1080, 5, "samsung galaxy on7 prime 2018 (sm-g611)"], [2033, 1080, 8, "huawei p smart (fig-xxx)"], [2040, 1080, 9, "hua<PERSON> maimang 6 (rne-xxx)"], [1920, 1200, 5, "samsung galaxy tab a 10.1 (sm-t580, sm-t585)"]]], ["arm mali-t860", "860", "arm mali-t860", 0, [[1184, 720, 8, "tcl a626"], [1196, 720, 8, "green orange go t2"], [1280, 720, 14, "htc one a9s"], [1920, 1024, 17, "hardkernel odroid-n1 (development board)"], [1920, 1032, 16, "contextmedia p-wal-108-elc-02"], [1920, 1080, 7, "htc u play (u-2u)"], [1920, 1116, 15, "imuz revolution a8"], [1920, 1128, 15, "rockchip rk3399 (development board)"], [2400, 1440, 10, "samsung chromebook plus (kevin)"]]], ["arm mali-t860 mp2", "860", "arm mali-t860 mp2", 0, [[598, 480, 25, "cipherlab 9700a"], [1184, 720, 11, "vernee m5"], [1193, 720, 11, "lg x power 2 (u+, x500, m-x320, m320)"], [1196, 720, 11, "lava z25"], [1199, 720, 11, "lg stylus 3 (m400)"], [1212, 720, 11, "meeg 306"], [1280, 720, 10, "oppo r66"], [1336, 720, 13, "asus pegasus 4s (x018d zb570tl)"], [1344, 720, 13, "allview x4 soul infinity n"], [1776, 1080, 7, "benq f55"], [1794, 1080, 6, "alcatel 7070"], [1798, 1080, 7, "energy sistem energy phone pro 3"], [1806, 1080, 8, "tecno phantom 6"], [1807, 1080, 7, "covia fleaz cp-j55a g07"], [1810, 1080, 8, "archos 55 diamond 2 plus"], [1920, 1080, 4, "advan vandroid i55c"], [2004, 1080, 7, "asus zenfone max plus m1 (x018d zb570tl)"], [1920, 1128, 7, "verizon qtaxia1"]]], ["arm mali-t880", "880", "arm mali-t880", 0, [[1184, 720, 22, "doogee mix"], [1280, 720, 20, "lenovo k8"], [1344, 720, 18, "casper via f2"], [1776, 1080, 11, "alcatel 6060 (mali-t880)"], [1794, 1080, 20, "huawei mate 8 (nxt-xxx)"], [1824, 1080, 11, "meiigoo m1"], [1920, 1080, 10, "letv leeco lex650"], [2016, 1080, 9, "vernee mix 2"], [2064, 1080, 10, "umi s2 pro"], [2392, 1440, 12, "huawei honor v8 (knt-al20)"], [2434, 1440, 11, "huawei honor note 8 premium edition (edi-al10)"], [2560, 1440, 24, "meizu pro 6 plus"], [2560, 1480, 10, "huawei dtab compact d-01j (docomo)"], [2560, 1600, 10, "huawei mediapad m3 (btv-xxx)"]]], ["arm mali-t880 mp12", "880", "arm mali-t880 mp12", 0, [[1920, 1080, 44, "samsung galaxy s7 (sm-g930f)"], [2560, 1440, 27, "samsung galaxy note 7 (mali-t880, sm-n930)"]]], ["arm mali-t880 mp2", "880", "arm mali-t880 mp2", 0, [[1184, 720, 19, "sony pikachu"], [1376, 720, 17, "umi s2"], [1776, 1080, 10, "coolpad a9s-9"], [1800, 1080, 11, "infinix x603"], [1920, 1080, 9, "innjoo pro2"]]], ["arm mali-t880 mp4", "880", "arm mali-t880 mp4", 0, [[1280, 672, 18, "mediatek x20 (development board)"], [1794, 1080, 13, "infocus tsp"], [1800, 1080, 16, "infinix x602 zero 4 plus"], [1806, 1080, 16, "tecno phantom a9"], [1810, 1080, 8, "mobiistar prime x pro"], [1815, 1080, 16, "tecno phantom 6 plus"], [1920, 1080, 7, "elephone r9"], [2048, 1440, 12, "brown tab 1"], [2392, 1440, 12, "vernee apollo"], [2416, 1440, 11, "freetel ftj162b kiwami2"], [2560, 1440, 10, "ivvi i5"]]], ["mali-g71", "71", "mali-g71", 0, [[2220, 1080, 54, "samsung s8+ sm-g955f"]]], ["mali-g72", "72", "mali-g72", 0, [[2220, 1080, 56, "samsung s9+ sm-g965f"]]], ["mali-t830", "830", "mali-t830", 0, [[1480, 720, 10, "samsung gm-j600fn"]]]]