import React from 'react'

const inputStyle = {
  width: '100%',
  padding: '0.5rem',
  background: '#444',
  border: '1px solid #666',
  borderRadius: '4px',
  color: 'white',
  fontSize: '0.9rem'
}

const labelStyle = {
  display: 'block',
  marginBottom: '0.3rem',
  fontSize: '0.8rem',
  fontWeight: 'bold',
  color: '#cc5500'
}

// Power Configuration Tab
export function PowerTab({ config, updateConfig }) {
  const handleChange = (section, field, value) => {
    updateConfig('power', {
      ...config,
      [section]: {
        ...config[section],
        [field]: value
      }
    })
  }

  return (
    <div>
      <h3 style={{ color: '#cc5500', marginBottom: '1rem' }}>🔋 Power Systems</h3>
      
      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Battery Configuration</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Battery Type:</label>
          <select
            value={config.battery.type}
            onChange={(e) => handleChange('battery', 'type', e.target.value)}
            style={inputStyle}
          >
            <option value="Lithium Ion">Lithium Ion</option>
            <option value="LiFePO4">LiFePO4</option>
            <option value="Silver Zinc">Silver Zinc</option>
            <option value="Alkaline">Alkaline</option>
          </select>
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Capacity:</label>
          <input
            type="text"
            value={config.battery.capacity}
            onChange={(e) => handleChange('battery', 'capacity', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 10kWh"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Voltage:</label>
          <input
            type="text"
            value={config.battery.voltage}
            onChange={(e) => handleChange('battery', 'voltage', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 48V"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Number of Cells:</label>
          <input
            type="number"
            value={config.battery.cells}
            onChange={(e) => handleChange('battery', 'cells', parseInt(e.target.value) || 16)}
            style={inputStyle}
            placeholder="16"
          />
        </div>
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Endurance</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Cruise Endurance:</label>
          <input
            type="text"
            value={config.endurance.cruise}
            onChange={(e) => handleChange('endurance', 'cruise', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 20 hours"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Survey Endurance:</label>
          <input
            type="text"
            value={config.endurance.survey}
            onChange={(e) => handleChange('endurance', 'survey', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 16 hours"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Emergency Reserve:</label>
          <input
            type="text"
            value={config.endurance.emergency}
            onChange={(e) => handleChange('endurance', 'emergency', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 24 hours"
          />
        </div>
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Power Consumption</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Propulsion:</label>
          <input
            type="text"
            value={config.consumption.propulsion}
            onChange={(e) => handleChange('consumption', 'propulsion', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 200W"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Sensors:</label>
          <input
            type="text"
            value={config.consumption.sensors}
            onChange={(e) => handleChange('consumption', 'sensors', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 150W"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Navigation:</label>
          <input
            type="text"
            value={config.consumption.navigation}
            onChange={(e) => handleChange('consumption', 'navigation', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 50W"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Communication:</label>
          <input
            type="text"
            value={config.consumption.communication}
            onChange={(e) => handleChange('consumption', 'communication', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 30W"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Total Power:</label>
          <input
            type="text"
            value={config.consumption.total}
            onChange={(e) => handleChange('consumption', 'total', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 430W"
          />
        </div>
      </div>
    </div>
  )
}

// Payload Configuration Tab
export function PayloadTab({ config, updateConfig }) {
  const handleChange = (field, value) => {
    updateConfig('payload', { [field]: value })
  }

  const handleCOGChange = (axis, value) => {
    updateConfig('payload', {
      centerOfGravity: {
        ...config.centerOfGravity,
        [axis]: parseFloat(value) || 0
      }
    })
  }

  return (
    <div>
      <h3 style={{ color: '#cc5500', marginBottom: '1rem' }}>📦 Payload Configuration</h3>
      
      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Weight & Volume</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Maximum Capacity:</label>
          <input
            type="text"
            value={config.maxCapacity}
            onChange={(e) => handleChange('maxCapacity', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 50kg"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Current Weight:</label>
          <input
            type="text"
            value={config.currentWeight}
            onChange={(e) => handleChange('currentWeight', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 25kg"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Maximum Volume:</label>
          <input
            type="text"
            value={config.maxVolume}
            onChange={(e) => handleChange('maxVolume', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 0.2m³"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Current Volume:</label>
          <input
            type="text"
            value={config.currentVolume}
            onChange={(e) => handleChange('currentVolume', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 0.1m³"
          />
        </div>
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Center of Gravity</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>X-offset (m):</label>
          <input
            type="number"
            step="0.001"
            value={config.centerOfGravity.x}
            onChange={(e) => handleCOGChange('x', e.target.value)}
            style={inputStyle}
            placeholder="0.000"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Y-offset (m):</label>
          <input
            type="number"
            step="0.001"
            value={config.centerOfGravity.y}
            onChange={(e) => handleCOGChange('y', e.target.value)}
            style={inputStyle}
            placeholder="0.000"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Z-offset (m):</label>
          <input
            type="number"
            step="0.001"
            value={config.centerOfGravity.z}
            onChange={(e) => handleCOGChange('z', e.target.value)}
            style={inputStyle}
            placeholder="0.000"
          />
        </div>
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Buoyancy</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Buoyancy State:</label>
          <select
            value={config.buoyancy}
            onChange={(e) => handleChange('buoyancy', e.target.value)}
            style={inputStyle}
          >
            <option value="Positive">Positive</option>
            <option value="Neutral">Neutral</option>
            <option value="Negative">Negative</option>
          </select>
        </div>
      </div>
    </div>
  )
}

// Communication Configuration Tab
export function CommunicationTab({ config, updateConfig }) {
  const handleChange = (section, field, value) => {
    updateConfig('communication', {
      ...config,
      [section]: {
        ...config[section],
        [field]: value
      }
    })
  }

  return (
    <div>
      <h3 style={{ color: '#cc5500', marginBottom: '1rem' }}>📻 Communication Systems</h3>
      
      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Acoustic Modem</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>
            <input
              type="checkbox"
              checked={config.acoustic.enabled}
              onChange={(e) => handleChange('acoustic', 'enabled', e.target.checked)}
              style={{ marginRight: '0.5rem' }}
            />
            Enable Acoustic Communication
          </label>
        </div>

        {config.acoustic.enabled && (
          <>
            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Model:</label>
              <input
                type="text"
                value={config.acoustic.model}
                onChange={(e) => handleChange('acoustic', 'model', e.target.value)}
                style={inputStyle}
                placeholder="e.g., EvoLogics S2C R 18/34"
              />
            </div>

            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Frequency:</label>
              <select
                value={config.acoustic.frequency}
                onChange={(e) => handleChange('acoustic', 'frequency', e.target.value)}
                style={inputStyle}
              >
                <option value="12kHz">12 kHz</option>
                <option value="18kHz">18 kHz</option>
                <option value="24kHz">24 kHz</option>
              </select>
            </div>

            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Range:</label>
              <input
                type="text"
                value={config.acoustic.range}
                onChange={(e) => handleChange('acoustic', 'range', e.target.value)}
                style={inputStyle}
                placeholder="e.g., 3000m"
              />
            </div>

            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Data Rate:</label>
              <input
                type="text"
                value={config.acoustic.dataRate}
                onChange={(e) => handleChange('acoustic', 'dataRate', e.target.value)}
                style={inputStyle}
                placeholder="e.g., 80bps"
              />
            </div>
          </>
        )}
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>WiFi Communication</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>
            <input
              type="checkbox"
              checked={config.wifi.enabled}
              onChange={(e) => handleChange('wifi', 'enabled', e.target.checked)}
              style={{ marginRight: '0.5rem' }}
            />
            Enable WiFi
          </label>
        </div>

        {config.wifi.enabled && (
          <>
            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Standard:</label>
              <select
                value={config.wifi.standard}
                onChange={(e) => handleChange('wifi', 'standard', e.target.value)}
                style={inputStyle}
              >
                <option value="802.11n">802.11n</option>
                <option value="802.11ac">802.11ac</option>
                <option value="802.11ax">802.11ax (WiFi 6)</option>
              </select>
            </div>

            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Range:</label>
              <input
                type="text"
                value={config.wifi.range}
                onChange={(e) => handleChange('wifi', 'range', e.target.value)}
                style={inputStyle}
                placeholder="e.g., 100m"
              />
            </div>
          </>
        )}
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Emergency Beacon</h4>
        
        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>
            <input
              type="checkbox"
              checked={config.emergency.beacon}
              onChange={(e) => handleChange('emergency', 'beacon', e.target.checked)}
              style={{ marginRight: '0.5rem' }}
            />
            Emergency Beacon
          </label>
        </div>

        {config.emergency.beacon && (
          <>
            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Frequency:</label>
              <input
                type="text"
                value={config.emergency.frequency}
                onChange={(e) => handleChange('emergency', 'frequency', e.target.value)}
                style={inputStyle}
                placeholder="e.g., 406MHz"
              />
            </div>

            <div style={{ marginBottom: '0.5rem' }}>
              <label style={labelStyle}>Battery Life:</label>
              <input
                type="text"
                value={config.emergency.battery}
                onChange={(e) => handleChange('emergency', 'battery', e.target.value)}
                style={inputStyle}
                placeholder="e.g., 48 hours"
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

// Operating Parameters Tab
export function OperatingTab({ config, updateConfig }) {
  const handleChange = (field, value) => {
    updateConfig('operating', { [field]: value })
  }

  return (
    <div>
      <h3 style={{ color: '#cc5500', marginBottom: '1rem' }}>⚙️ Operating Parameters</h3>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Depth & Speed</h4>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Maximum Depth:</label>
          <input
            type="text"
            value={config.maxDepth}
            onChange={(e) => handleChange('maxDepth', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 3000m"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Cruise Speed:</label>
          <input
            type="text"
            value={config.cruiseSpeed}
            onChange={(e) => handleChange('cruiseSpeed', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 1.5 m/s"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Survey Speed:</label>
          <input
            type="text"
            value={config.surveySpeed}
            onChange={(e) => handleChange('surveySpeed', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 1.0 m/s"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Maximum Speed:</label>
          <input
            type="text"
            value={config.maxSpeed}
            onChange={(e) => handleChange('maxSpeed', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 2.5 m/s"
          />
        </div>
      </div>

      <div style={{ marginBottom: '1.5rem' }}>
        <h4 style={{ color: '#dd6600', marginBottom: '0.5rem' }}>Maneuvering</h4>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Turn Radius:</label>
          <input
            type="text"
            value={config.turnRadius}
            onChange={(e) => handleChange('turnRadius', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 10m"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Ascent Rate:</label>
          <input
            type="text"
            value={config.ascentRate}
            onChange={(e) => handleChange('ascentRate', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 0.5 m/s"
          />
        </div>

        <div style={{ marginBottom: '0.5rem' }}>
          <label style={labelStyle}>Descent Rate:</label>
          <input
            type="text"
            value={config.descentRate}
            onChange={(e) => handleChange('descentRate', e.target.value)}
            style={inputStyle}
            placeholder="e.g., 0.5 m/s"
          />
        </div>
      </div>
    </div>
  )
}
