# HUGIN 3000 AUV Sensor Offset Visualizer

A 3D visualization tool for managing and documenting sensor offsets on Kongsberg HUGIN 3000-class Autonomous Underwater Vehicles (AUVs).

## Features

- **3D Visualization**: Interactive 3D model with sensor placement visualization
- **Sensor Management**: Add, edit, and delete sensor configurations
- **Data Export**: Export sensor data to CSV and comprehensive PDF reports
- **Dark/Light Theme**: Toggle between dark and light themes
- **Academic References**: Includes proper citations for sensor specifications
- **Error Handling**: Robust error boundaries and validation
- **Responsive Design**: Works on desktop and tablet devices

## Quick Start

### Prerequisites

- Node.js (v16 or higher)
- pnpm, npm, or yarn package manager

### Installation

1. **Clone or download the project**
2. **Navigate to the project directory**
   ```bash
   cd AUV/Offsets
   ```

3. **Install dependencies**
   ```bash
   pnpm install
   # or
   npm install
   # or
   yarn install
   ```

4. **Start the development server**
   ```bash
   pnpm dev
   # or
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**
   The application will automatically open at `http://localhost:3000`

## Usage

### Adding Sensors
1. Click the "Add Sensor" button
2. Enter sensor name, position coordinates (X, Y, Z in meters)
3. Fill in metadata: type, serial number, and notes
4. The sensor will appear in the 3D visualization

### Coordinate System
- **X-axis**: Forward (positive = bow direction)
- **Y-axis**: Starboard (positive = right side)
- **Z-axis**: Down (positive = deeper)
- **Units**: Meters
- **Origin**: Common Reference Point (CRP) at vehicle center

### Exporting Data
- **CSV Export**: Exports sensor data in comma-separated format
- **PDF Report**: Generates comprehensive report with 3D visualization snapshot

### Theme Toggle
Click the theme toggle button (☀️/🌙) in the top-right corner to switch between dark and light themes.

## Technical Details

### Built With
- **React 19** - UI framework (JavaScript, no TypeScript required)
- **Three.js** - 3D graphics
- **@react-three/fiber** - React Three.js renderer
- **@react-three/drei** - Three.js helpers
- **Vite** - Build tool and dev server
- **jsPDF** - PDF generation
- **html2canvas** - Canvas screenshots

### Project Structure
```
src/
├── App.jsx           # Main application component
├── components.jsx    # Reusable UI components
├── index.jsx        # Application entry point
└── styles.css       # Global styles

public/
└── model.glb        # 3D AUV model file
```

## Development

### Building for Production
```bash
pnpm build
```

### Running Tests
```bash
pnpm test
```

## Academic References

The sensor specifications include proper academic citations. See the References section in the application for detailed documentation sources.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is intended for educational and research purposes. Sensor specifications should be verified with manufacturer documentation for operational use.

## Support

For issues or questions, please check the browser console for error messages and ensure all dependencies are properly installed.
