import jsPDF from 'jspdf'
import 'jspdf-autotable'

// Test function to verify jsPDF is working
export function testPDFExport() {
  try {
    console.log('Testing PDF export...')
    const doc = new jsPDF()
    doc.text('Test PDF', 20, 20)
    doc.save('test.pdf')
    console.log('Test PDF created successfully!')
    return true
  } catch (error) {
    console.error('Test PDF failed:', error)
    return false
  }
}

// Vehicle specifications
const VEHICLE_SPECS = {
  hugin3000: {
    name: 'Hugin 3000',
    length: '3.5m',
    diameter: '0.4m',
    weight: '650kg',
    maxDepth: '3000m',
    endurance: '20+ hours',
    speed: '1.5 m/s',
    description: 'Long-range autonomous underwater vehicle for deep-sea operations'
  },
  hugin1000: {
    name: 'Hugin 1000',
    length: '2.1m', 
    diameter: '0.24m',
    weight: '200kg',
    maxDepth: '1000m',
    endurance: '10+ hours',
    speed: '1.8 m/s',
    description: 'Compact AUV designed for coastal and shallow water surveys'
  }
}

export function exportToPDF(sensors, selectedVehicle, modelType) {
  try {
    console.log('Starting PDF export...', { sensors, selectedVehicle, modelType })

    // Create a simple PDF first to test
    const doc = new jsPDF()

    // Add basic content
    doc.setFontSize(16)
    doc.text('AUV Sensor Configuration Report', 20, 20)
    doc.setFontSize(12)
    doc.text(`Vehicle: ${selectedVehicle}`, 20, 40)
    doc.text(`Sensors: ${sensors.length}`, 20, 60)
    doc.text(`Model Type: ${modelType}`, 20, 80)

    // Save the PDF
    const filename = `AUV_Sensor_Config_${new Date().getTime()}.pdf`
    console.log('Saving simple PDF with filename:', filename)
    doc.save(filename)
    console.log('Simple PDF export completed successfully!')

    return // Exit early for testing

    const vehicleSpec = VEHICLE_SPECS[selectedVehicle]
    const currentDate = new Date().toLocaleDateString()

    console.log('PDF document created, vehicle spec:', vehicleSpec)
  
  // Page setup
  const pageWidth = doc.internal.pageSize.width
  const pageHeight = doc.internal.pageSize.height
  const margin = 20
  let yPosition = margin

  // Header
  doc.setFontSize(20)
  doc.setFont('helvetica', 'bold')
  doc.text('AUV Sensor Configuration Report', pageWidth / 2, yPosition, { align: 'center' })
  yPosition += 15

  doc.setFontSize(12)
  doc.setFont('helvetica', 'normal')
  doc.text(`Generated: ${currentDate}`, pageWidth / 2, yPosition, { align: 'center' })
  yPosition += 20

  // Vehicle Information Section
  doc.setFontSize(16)
  doc.setFont('helvetica', 'bold')
  doc.text('Vehicle Specifications', margin, yPosition)
  yPosition += 10

  // Vehicle specs table
  const vehicleData = [
    ['Vehicle Model', vehicleSpec.name],
    ['Length', vehicleSpec.length],
    ['Diameter', vehicleSpec.diameter],
    ['Weight', vehicleSpec.weight],
    ['Maximum Depth', vehicleSpec.maxDepth],
    ['Endurance', vehicleSpec.endurance],
    ['Cruise Speed', vehicleSpec.speed],
    ['Model Type', modelType === 'custom' ? 'Custom GLB' : modelType === 'auto' ? 'Auto-loaded' : 'Procedural'],
    ['Total Sensors', sensors.length.toString()]
  ]

  console.log('Creating vehicle specs table...')

  // Check if autoTable is available
  if (typeof doc.autoTable !== 'function') {
    console.error('autoTable is not available on jsPDF instance')
    throw new Error('jsPDF autoTable plugin not loaded properly')
  }

  doc.autoTable({
    startY: yPosition,
    head: [['Parameter', 'Value']],
    body: vehicleData,
    theme: 'grid',
    headStyles: { fillColor: [204, 85, 0] }, // Dark orange header
    styles: { fontSize: 10 },
    columnStyles: {
      0: { fontStyle: 'bold', cellWidth: 60 },
      1: { cellWidth: 80 }
    }
  })

  console.log('Vehicle specs table created successfully')

  yPosition = doc.lastAutoTable.finalY + 15

  // Description
  doc.setFontSize(10)
  doc.setFont('helvetica', 'italic')
  const splitDescription = doc.splitTextToSize(vehicleSpec.description, pageWidth - 2 * margin)
  doc.text(splitDescription, margin, yPosition)
  yPosition += splitDescription.length * 5 + 15

  // Coordinate System Reference
  doc.setFontSize(14)
  doc.setFont('helvetica', 'bold')
  doc.text('Coordinate System (Maritime Convention)', margin, yPosition)
  yPosition += 10

  doc.setFontSize(10)
  doc.setFont('helvetica', 'normal')
  doc.setTextColor(204, 68, 68) // Red
  doc.text('• X-axis (Starboard): Positive = Right side of vehicle', margin + 5, yPosition)
  yPosition += 6
  doc.setTextColor(68, 204, 68) // Green
  doc.text('• Y-axis (Forward): Positive = Bow direction', margin + 5, yPosition)
  yPosition += 6
  doc.setTextColor(68, 68, 204) // Blue
  doc.text('• Z-axis (Down): Positive = Deeper', margin + 5, yPosition)
  yPosition += 6
  doc.setTextColor(0, 0, 0) // Reset to black
  doc.text('• Units: Meters from vehicle center reference point', margin + 5, yPosition)
  yPosition += 15

  // Add 3D AUV diagram
  drawAUVDiagram(doc, margin, yPosition, selectedVehicle)
  yPosition += 80

  // Check if we need a new page
  if (yPosition > pageHeight - 100) {
    doc.addPage()
    yPosition = margin
  }

  // Sensor Configuration Table
  doc.setFontSize(16)
  doc.setFont('helvetica', 'bold')
  doc.text('Sensor Configuration', margin, yPosition)
  yPosition += 10

  // Prepare sensor data
  const sensorData = sensors.map((sensor, index) => [
    (index + 1).toString(),
    sensor.name,
    sensor.position[0].toFixed(3),
    sensor.position[1].toFixed(3), 
    sensor.position[2].toFixed(3),
    sensor.type.charAt(0).toUpperCase() + sensor.type.slice(1),
    sensor.color
  ])

  // Sensor table
  doc.autoTable({
    startY: yPosition,
    head: [['#', 'Sensor Name', 'X (m)', 'Y (m)', 'Z (m)', 'Type', 'Color']],
    body: sensorData,
    theme: 'striped',
    headStyles: { 
      fillColor: [204, 85, 0], // Dark orange header
      textColor: [255, 255, 255],
      fontStyle: 'bold'
    },
    styles: { 
      fontSize: 9,
      cellPadding: 3
    },
    columnStyles: {
      0: { cellWidth: 15, halign: 'center' },
      1: { cellWidth: 50 },
      2: { cellWidth: 20, halign: 'right' },
      3: { cellWidth: 20, halign: 'right' },
      4: { cellWidth: 20, halign: 'right' },
      5: { cellWidth: 25 },
      6: { cellWidth: 25 }
    },
    alternateRowStyles: { fillColor: [245, 245, 245] }
  })

  // Footer
  const finalY = doc.lastAutoTable.finalY + 20
  doc.setFontSize(8)
  doc.setFont('helvetica', 'italic')
  doc.text('Generated by AUV Sensor Studio', margin, finalY)
  doc.text(`Page 1 of 1`, pageWidth - margin - 30, finalY)

  // Save the PDF
  const filename = `${vehicleSpec.name.replace(' ', '_')}_Sensor_Config_${currentDate.replace(/\//g, '-')}.pdf`
  console.log('Saving PDF with filename:', filename)
  doc.save(filename)
  console.log('PDF export completed successfully!')

  } catch (error) {
    console.error('PDF export failed:', error)
    alert(`PDF export failed: ${error.message}`)
  }
}

// Function to draw a simple AUV diagram
function drawAUVDiagram(doc, x, y, vehicleType) {
  const centerX = x + 90
  const centerY = y + 40
  
  // Draw AUV outline (top view)
  doc.setDrawColor(204, 85, 0) // Dark orange
  doc.setFillColor(255, 220, 180) // Light orange fill
  doc.setLineWidth(1)
  
  // Main hull (ellipse approximation)
  const hullLength = vehicleType === 'hugin3000' ? 60 : 45
  const hullWidth = vehicleType === 'hugin3000' ? 12 : 8
  
  doc.ellipse(centerX, centerY, hullLength/2, hullWidth/2, 'FD')
  
  // Nose cone
  doc.setFillColor(204, 85, 0)
  doc.triangle(centerX + hullLength/2, centerY, 
               centerX + hullLength/2 + 10, centerY - 5,
               centerX + hullLength/2 + 10, centerY + 5, 'F')
  
  // Fins
  doc.setFillColor(180, 180, 180)
  // Top fin
  doc.rect(centerX - hullLength/4, centerY - hullWidth/2 - 8, 15, 8, 'F')
  // Bottom fin  
  doc.rect(centerX - hullLength/4, centerY + hullWidth/2, 15, 8, 'F')
  // Side fins
  doc.rect(centerX - hullLength/3, centerY - 3, 8, 6, 'F')
  doc.rect(centerX - hullLength/3, centerY - 3, 8, 6, 'F')
  
  // Propeller
  doc.setFillColor(100, 100, 100)
  doc.circle(centerX - hullLength/2 - 5, centerY, 4, 'F')
  
  // Forward direction arrow and label
  doc.setDrawColor(0, 150, 0) // Green
  doc.setLineWidth(2)
  doc.line(centerX + hullLength/2 + 20, centerY, centerX + hullLength/2 + 35, centerY)
  doc.triangle(centerX + hullLength/2 + 35, centerY,
               centerX + hullLength/2 + 30, centerY - 3,
               centerX + hullLength/2 + 30, centerY + 3, 'F')
  
  doc.setFontSize(12)
  doc.setFont('helvetica', 'bold')
  doc.setTextColor(0, 150, 0)
  doc.text('FWD', centerX + hullLength/2 + 40, centerY + 4)
  
  // Coordinate axes
  doc.setDrawColor(200, 50, 50) // Red for X
  doc.setLineWidth(1)
  doc.line(centerX, centerY + hullWidth/2 + 20, centerX + 20, centerY + hullWidth/2 + 20)
  doc.text('X (Starboard)', centerX + 25, centerY + hullWidth/2 + 24)
  
  doc.setDrawColor(50, 200, 50) // Green for Y
  doc.line(centerX - hullLength/2 - 20, centerY, centerX - hullLength/2 - 20, centerY - 20)
  doc.text('Y (Forward)', centerX - hullLength/2 - 15, centerY - 25)
  
  // Vehicle label
  doc.setTextColor(0, 0, 0)
  doc.setFontSize(10)
  doc.setFont('helvetica', 'bold')
  doc.text(`${VEHICLE_SPECS[vehicleType].name} (Top View)`, centerX - 30, y - 5)
  
  // Reset colors
  doc.setDrawColor(0, 0, 0)
  doc.setTextColor(0, 0, 0)
}
