import jsPDF from 'jspdf'



// Vehicle specifications
const VEHICLE_SPECS = {
  hugin3000: {
    name: 'Hugin 3000',
    length: '3.5m',
    diameter: '0.4m',
    weight: '650kg',
    maxDepth: '3000m',
    endurance: '20+ hours',
    speed: '1.5 m/s',
    description: 'Long-range autonomous underwater vehicle for deep-sea operations'
  },
  hugin1000: {
    name: 'Hugin 1000',
    length: '2.1m', 
    diameter: '0.24m',
    weight: '200kg',
    maxDepth: '1000m',
    endurance: '10+ hours',
    speed: '1.8 m/s',
    description: 'Compact AUV designed for coastal and shallow water surveys'
  }
}

export function exportToPDF(sensors, selectedVehicle, modelType) {
  try {
    console.log('Starting PDF export...', { sensors, selectedVehicle, modelType })

    const doc = new jsPDF()
    const vehicleSpec = VEHICLE_SPECS[selectedVehicle]
    const currentDate = new Date().toLocaleDateString()

    console.log('PDF document created, vehicle spec:', vehicleSpec)

    // Page setup
    const pageWidth = doc.internal.pageSize.width
    const margin = 20
    let yPosition = margin

    // Header
    doc.setFontSize(20)
    doc.setFont('helvetica', 'bold')
    doc.text('AUV Sensor Configuration Report', pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 15

    doc.setFontSize(12)
    doc.setFont('helvetica', 'normal')
    doc.text(`Generated: ${currentDate}`, pageWidth / 2, yPosition, { align: 'center' })
    yPosition += 25

    // Vehicle Information Section
    doc.setFontSize(16)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(204, 85, 0) // Dark orange
    doc.text('Vehicle Specifications', margin, yPosition)
    doc.setTextColor(0, 0, 0) // Reset to black
    yPosition += 15

    // Vehicle specs - manual table
    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')

    const specs = [
      ['Vehicle Model:', vehicleSpec.name],
      ['Length:', vehicleSpec.length],
      ['Diameter:', vehicleSpec.diameter],
      ['Weight:', vehicleSpec.weight],
      ['Maximum Depth:', vehicleSpec.maxDepth],
      ['Endurance:', vehicleSpec.endurance],
      ['Cruise Speed:', vehicleSpec.speed],
      ['Model Type:', modelType === 'custom' ? 'Custom GLB' : modelType === 'auto' ? 'Auto-loaded' : 'Procedural'],
      ['Total Sensors:', sensors.length.toString()]
    ]

    specs.forEach(([label, value]) => {
      doc.setFont('helvetica', 'bold')
      doc.text(label, margin + 5, yPosition)
      doc.setFont('helvetica', 'normal')
      doc.text(value, margin + 80, yPosition)
      yPosition += 8
    })

    // Description
    doc.setFontSize(10)
    doc.setFont('helvetica', 'italic')
    const description = vehicleSpec.description
    const splitDescription = doc.splitTextToSize(description, pageWidth - 2 * margin)
    doc.text(splitDescription, margin, yPosition)
    yPosition += splitDescription.length * 5 + 15

    // Coordinate System Reference
    doc.setFontSize(14)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(204, 85, 0) // Dark orange
    doc.text('Coordinate System (Maritime Convention)', margin, yPosition)
    doc.setTextColor(0, 0, 0) // Reset to black
    yPosition += 15

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.setTextColor(204, 68, 68) // Red
    doc.text('• X-axis (Starboard): Positive = Right side of vehicle', margin + 5, yPosition)
    yPosition += 6
    doc.setTextColor(68, 204, 68) // Green
    doc.text('• Y-axis (Forward): Positive = Bow direction', margin + 5, yPosition)
    yPosition += 6
    doc.setTextColor(68, 68, 204) // Blue
    doc.text('• Z-axis (Down): Positive = Deeper', margin + 5, yPosition)
    yPosition += 6
    doc.setTextColor(0, 0, 0) // Reset to black
    doc.text('• Units: Meters from vehicle center reference point', margin + 5, yPosition)
    yPosition += 20

    // Sensor Configuration Table
    doc.setFontSize(16)
    doc.setFont('helvetica', 'bold')
    doc.setTextColor(204, 85, 0) // Dark orange
    doc.text('Sensor Configuration', margin, yPosition)
    doc.setTextColor(0, 0, 0) // Reset to black
    yPosition += 15

    // Manual sensor table header
    doc.setFontSize(10)
    doc.setFont('helvetica', 'bold')
    doc.text('#', margin + 5, yPosition)
    doc.text('Sensor Name', margin + 20, yPosition)
    doc.text('X (m)', margin + 80, yPosition)
    doc.text('Y (m)', margin + 110, yPosition)
    doc.text('Z (m)', margin + 140, yPosition)
    doc.text('Type', margin + 170, yPosition)
    yPosition += 8

    // Draw header line
    doc.setLineWidth(0.5)
    doc.line(margin, yPosition - 2, pageWidth - margin, yPosition - 2)
    yPosition += 5

    // Sensor data rows
    doc.setFont('helvetica', 'normal')
    sensors.forEach((sensor, index) => {
      doc.text((index + 1).toString(), margin + 5, yPosition)
      doc.text(sensor.name, margin + 20, yPosition)
      doc.text(sensor.position[0].toFixed(3), margin + 80, yPosition)
      doc.text(sensor.position[1].toFixed(3), margin + 110, yPosition)
      doc.text(sensor.position[2].toFixed(3), margin + 140, yPosition)
      doc.text(sensor.type.charAt(0).toUpperCase() + sensor.type.slice(1), margin + 170, yPosition)
      yPosition += 8
    })

    yPosition += 10

    // Footer
    doc.setFontSize(8)
    doc.setFont('helvetica', 'italic')
    doc.text('Generated by AUV Sensor Studio', margin, yPosition)
    doc.text(`Page 1 of 1`, pageWidth - margin - 30, yPosition)

    // Save the PDF
    const filename = `${vehicleSpec.name.replace(' ', '_')}_Sensor_Config_${currentDate.replace(/\//g, '-')}.pdf`
    console.log('Saving PDF with filename:', filename)
    doc.save(filename)
    console.log('PDF export completed successfully!')

  } catch (error) {
    console.error('PDF export failed:', error)
    alert(`PDF export failed: ${error.message}`)
  }
}

// Simplified PDF export without complex diagrams
// Future enhancement: Add 3D AUV diagram using basic shapes
