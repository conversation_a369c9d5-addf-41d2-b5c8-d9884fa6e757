function r(){return function(r){var e={R:"13k,1a,2,3,3,2+1j,ch+16,a+1,5+2,2+n,5,a,4,6+16,4+3,h+1b,4mo,179q,2+9,2+11,2i9+7y,2+68,4,3+4,5+13,4+3,2+4k,3+29,8+cf,1t+7z,w+17,3+3m,1t+3z,16o1+5r,8+30,8+mc,29+1r,29+4v,75+73",EN:"1c+9,3d+1,6,187+9,513,4+5,7+9,sf+j,175h+9,qw+q,161f+1d,4xt+a,25i+9",ES:"17,2,6dp+1,f+1,av,16vr,mx+1,4o,2",ET:"z+2,3h+3,b+1,ym,3e+1,2o,p4+1,8,6u,7c,g6,1wc,1n9+4,30+1b,2n,6d,qhx+1,h0m,a+1,49+2,63+1,4+1,6bb+3,12jj",AN:"16o+5,2j+9,2+1,35,ed,1ff2+9,87+u",CS:"18,2+1,b,2u,12k,55v,l,17v0,2,3,53,2+1,b",B:"a,3,f+2,2v,690",S:"9,2,k",WS:"c,k,4f4,1vk+a,u,1j,335",ON:"x+1,4+4,h+5,r+5,r+3,z,5+3,2+1,2+1,5,2+2,3+4,o,w,ci+1,8+d,3+d,6+8,2+g,39+1,9,6+1,2,33,b8,3+1,3c+1,7+1,5r,b,7h+3,sa+5,2,3i+6,jg+3,ur+9,2v,ij+1,9g+9,7+a,8m,4+1,49+x,14u,2+2,c+2,e+2,e+2,e+1,i+n,e+e,2+p,u+2,e+2,36+1,2+3,2+1,b,2+2,6+5,2,2,2,h+1,5+4,6+3,3+f,16+2,5+3l,3+81,1y+p,2+40,q+a,m+13,2r+ch,2+9e,75+hf,3+v,2+2w,6e+5,f+6,75+2a,1a+p,2+2g,d+5x,r+b,6+3,4+o,g,6+1,6+2,2k+1,4,2j,5h+z,1m+1,1e+f,t+2,1f+e,d+3,4o+3,2s+1,w,535+1r,h3l+1i,93+2,2s,b+1,3l+x,2v,4g+3,21+3,kz+1,g5v+1,5a,j+9,n+v,2,3,2+8,2+1,3+2,2,3,46+1,4+4,h+5,r+5,r+a,3h+2,4+6,b+4,78,1r+24,4+c,4,1hb,ey+6,103+j,16j+c,1ux+7,5+g,fsh,jdq+1t,4,57+2e,p1,1m,1m,1m,1m,4kt+1,7j+17,5+2r,d+e,3+e,2+e,2+10,m+4,w,1n+5,1q,4z+5,4b+rb,9+c,4+c,4+37,d+2g,8+b,l+b,5+1j,9+9,7+13,9+t,3+1,27+3c,2+29,2+3q,d+d,3+4,4+2,6+6,a+o,8+6,a+2,e+6,16+42,2+1i",BN:"0+8,6+d,2s+5,2+p,e,4m9,1kt+2,2b+5,5+5,17q9+v,7k,6p+8,6+1,119d+3,440+7,96s+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+75,6p+2rz,1ben+1,1ekf+1,1ekf+1",NSM:"lc+33,7o+6,7c+18,2,2+1,2+1,2,21+a,1d+k,h,2u+6,3+5,3+1,2+3,10,v+q,2k+a,1n+8,a,p+3,2+8,2+2,2+4,18+2,3c+e,2+v,1k,2,5+7,5,4+6,b+1,u,1n,5+3,9,l+1,r,3+1,1m,5+1,5+1,3+2,4,v+1,4,c+1,1m,5+4,2+1,5,l+1,n+5,2,1n,3,2+3,9,8+1,c+1,v,1q,d,1f,4,1m+2,6+2,2+3,8+1,c+1,u,1n,g+1,l+1,t+1,1m+1,5+3,9,l+1,u,21,8+2,2,2j,3+6,d+7,2r,3+8,c+5,23+1,s,2,2,1k+d,2+4,2+1,6+a,2+z,a,2v+3,2+5,2+1,3+1,q+1,5+2,h+3,e,3+1,7,g,jk+2,qb+2,u+2,u+1,v+1,1t+1,2+6,9,3+a,a,1a+2,3c+1,z,3b+2,5+1,a,7+2,64+1,3,1n,2+6,2,2,3+7,7+9,3,1d+g,1s+3,1d,2+4,2,6,15+8,d+1,x+3,3+1,2+2,1l,2+1,4,2+2,1n+7,3+1,49+2,2+c,2+6,5,7,4+1,5j+1l,2+4,k1+w,2db+2,3y,2p+v,ff+3,30+1,n9x+3,2+9,x+1,29+1,7l,4,5,q+1,6,48+1,r+h,e,13+7,q+a,1b+2,1d,3+3,3+1,14,1w+5,3+1,3+1,d,9,1c,1g,2+2,3+1,6+1,2,17+1,9,6n,3,5,fn5,ki+f,h+f,r2,6b,46+4,1af+2,2+1,6+3,15+2,5,4m+1,fy+3,as+1,4a+a,4x,1j+e,1l+2,1e+3,3+1,1y+2,11+4,2+7,1r,d+1,1h+8,b+3,3,2o+2,3,2+1,7,4h,4+7,m+1,1m+1,4,12+6,4+4,5g+7,3+2,2,o,2d+5,2,5+1,2+1,6n+3,7+1,2+1,s+1,2e+7,3,2+1,2z,2,3+5,2,2u+2,3+3,2+4,78+8,2+1,75+1,2,5,41+3,3+1,5,x+5,3+1,15+5,3+3,9,a+5,3+2,1b+c,2+1,bb+6,2+5,2d+l,3+6,2+1,2+1,3f+5,4,2+1,2+6,2,21+1,4,2,9o+1,f0c+4,1o+6,t5,1s+3,2a,f5l+1,43t+2,i+7,3+6,v+3,45+2,1j0+1i,5+1d,9,f,n+4,2+e,11t+6,2+g,3+6,2+1,2+4,7a+6,c6+3,15t+6,32+6,gzhy+6n",AL:"16w,3,2,e+1b,z+2,2+2s,g+1,8+1,b+m,2+t,s+2i,c+e,4h+f,1d+1e,1bwe+dp,3+3z,x+c,2+1,35+3y,2rm+z,5+7,b+5,dt+l,c+u,17nl+27,1t+27,4x+6n,3+d",LRO:"6ct",RLO:"6cu",LRE:"6cq",RLE:"6cr",PDF:"6cs",LRI:"6ee",RLI:"6ef",FSI:"6eg",PDI:"6eh"},f={},a={};f.L=1,a[1]="L",Object.keys(e).forEach((function(r,e){f[r]=1<<e+1,a[f[r]]=r})),Object.freeze(f);var n=f.LRI|f.RLI|f.FSI,v=f.L|f.R|f.AL,i=f.B|f.S|f.WS|f.ON|f.FSI|f.LRI|f.RLI|f.PDI,o=f.BN|f.RLE|f.LRE|f.RLO|f.LRO|f.PDF,t=f.S|f.WS|f.B|n|f.PDI|o,u=null;function l(r){return function(){if(!u){u=new Map;var r=function(r){if(e.hasOwnProperty(r)){var a=0;e[r].split(",").forEach((function(e){var n=e.split("+"),v=n[0],i=n[1];v=parseInt(v,36),i=i?parseInt(i,36):0,u.set(a+=v,f[r]);for(var o=0;o<i;o++)u.set(++a,f[r])}))}};for(var a in e)r(a)}}(),u.get(r.codePointAt(0))||f.L}var c,d,b,s="14>1,1e>2,u>2,2wt>1,1>1,1ge>1,1wp>1,1j>1,f>1,hm>1,1>1,u>1,u6>1,1>1,+5,28>1,w>1,1>1,+3,b8>1,1>1,+3,1>3,-1>-1,3>1,1>1,+2,1s>1,1>1,x>1,th>1,1>1,+2,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,4q>1,1e>2,u>2,2>1,+1",k="6f1>-6dx,6dy>-6dx,6ec>-6ed,6ee>-6ed,6ww>2jj,-2ji>2jj,14r4>-1e7l,1e7m>-1e7l,1e7m>-1e5c,1e5d>-1e5b,1e5c>-14qx,14qy>-14qx,14vn>-1ecg,1ech>-1ecg,1edu>-1ecg,1eci>-1ecg,1eda>-1ecg,1eci>-1ecg,1eci>-168q,168r>-168q,168s>-14ye,14yf>-14ye";function h(r,e){var f,a=0,n=new Map,v=e&&new Map;return r.split(",").forEach((function r(i){if(-1!==i.indexOf("+"))for(var o=+i;o--;)r(f);else{f=i;var t=i.split(">"),u=t[0],l=t[1];u=String.fromCodePoint(a+=parseInt(u,36)),l=String.fromCodePoint(a+=parseInt(l,36)),n.set(u,l),e&&v.set(l,u)}})),{map:n,reverseMap:v}}function m(){if(!c){var r=h(s,!0),e=r.map,f=r.reverseMap;c=e,d=f,b=h(k,!1).map}}function j(r){return m(),c.get(r)||null}function g(r){return m(),d.get(r)||null}function p(r){return m(),b.get(r)||null}var q=f.L,w=f.R,x=f.EN,_=f.ES,y=f.ET,M=f.AN,z=f.CS,I=f.B,L=f.S,S=f.ON,R=f.BN,O=f.NSM,E=f.AL,N=f.LRO,A=f.RLO,D=f.LRE,P=f.RLE,T=f.PDF,W=f.LRI,B=f.RLI,F=f.FSI,U=f.PDI;var C;function G(r){return function(){if(!C){var r=h("14>1,j>2,t>2,u>2,1a>g,2v3>1,1>1,1ge>1,1wd>1,b>1,1j>1,f>1,ai>3,-2>3,+1,8>1k0,-1jq>1y7,-1y6>1hf,-1he>1h6,-1h5>1ha,-1h8>1qi,-1pu>1,6>3u,-3s>7,6>1,1>1,f>1,1>1,+2,3>1,1>1,+13,4>1,1>1,6>1eo,-1ee>1,3>1mg,-1me>1mk,-1mj>1mi,-1mg>1mi,-1md>1,1>1,+2,1>10k,-103>1,1>1,4>1,5>1,1>1,+10,3>1,1>8,-7>8,+1,-6>7,+1,a>1,1>1,u>1,u6>1,1>1,+5,26>1,1>1,2>1,2>2,8>1,7>1,4>1,1>1,+5,b8>1,1>1,+3,1>3,-2>1,2>1,1>1,+2,c>1,3>1,1>1,+2,h>1,3>1,a>1,1>1,2>1,3>1,1>1,d>1,f>1,3>1,1a>1,1>1,6>1,7>1,13>1,k>1,1>1,+19,4>1,1>1,+2,2>1,1>1,+18,m>1,a>1,1>1,lk>1,1>1,4>1,2>1,f>1,3>1,1>1,+3,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,6>1,4j>1,j>2,t>2,u>2,2>1,+1",!0),e=r.map;r.reverseMap.forEach((function(r,f){e.set(f,r)})),C=e}}(),C.get(r)||null}function H(r,e,f,a){var n=r.length;f=Math.max(0,null==f?0:+f),a=Math.min(n-1,null==a?n-1:+a);var v=[];return e.paragraphs.forEach((function(n){var i=Math.max(f,n.start),o=Math.min(a,n.end);if(i<o){for(var u=e.levels.slice(i,o+1),c=o;c>=i&&l(r[c])&t;c--)u[c]=n.level;for(var d=n.level,b=1/0,s=0;s<u.length;s++){var k=u[s];k>d&&(d=k),k<b&&(b=1|k)}for(var h=d;h>=b;h--)for(var m=0;m<u.length;m++)if(u[m]>=h){for(var j=m;m+1<u.length&&u[m+1]>=h;)m++;m>j&&v.push([j+i,m+i])}}})),v}function J(r,e,f,a){for(var n=H(r,e,f,a),v=[],i=0;i<r.length;i++)v[i]=i;return n.forEach((function(r){for(var e=r[0],f=r[1],a=v.slice(e,f+1),n=a.length;n--;)v[f-n]=a[n]})),v}return r.closingToOpeningBracket=g,r.getBidiCharType=l,r.getBidiCharTypeName=function(r){return a[l(r)]},r.getCanonicalBracket=p,r.getEmbeddingLevels=function(r,e){for(var f=new Uint32Array(r.length),a=0;a<r.length;a++)f[a]=l(r[a]);var u=new Map;function c(r,e){var a=f[r];f[r]=e,u.set(a,u.get(a)-1),a&i&&u.set(i,u.get(i)-1),u.set(e,(u.get(e)||0)+1),e&i&&u.set(i,(u.get(i)||0)+1)}for(var d=new Uint8Array(r.length),b=new Map,s=[],k=null,h=0;h<r.length;h++)k||s.push(k={start:h,end:r.length-1,level:"rtl"===e?1:"ltr"===e?0:Fe(h,!1)}),f[h]&I&&(k.end=h,k=null);for(var m=P|D|A|N|n|U|T|I,C=function(r){return r+(1&r?1:2)},G=function(r){return r+(1&r?2:1)},H=0;H<s.length;H++){var J=[{v:(k=s[H]).level,i:0,o:0}],K=void 0,Q=0,V=0,X=0;u.clear();for(var Y=k.start;Y<=k.end;Y++){var Z=f[Y];if(K=J[J.length-1],u.set(Z,(u.get(Z)||0)+1),Z&i&&u.set(i,(u.get(i)||0)+1),Z&m)if(Z&(P|D)){d[Y]=K.v;var $=(Z===P?G:C)(K.v);$<=125&&!Q&&!V?J.push({v:$,i:0,o:0}):Q||V++}else if(Z&(A|N)){d[Y]=K.v;var rr=(Z===A?G:C)(K.v);rr<=125&&!Q&&!V?J.push({v:rr,i:Z&A?w:q,o:0}):Q||V++}else if(Z&n){Z&F&&(Z=1===Fe(Y+1,!0)?B:W),d[Y]=K.v,K.i&&c(Y,K.i);var er=(Z===B?G:C)(K.v);er<=125&&0===Q&&0===V?(X++,J.push({v:er,i:0,o:1,t:Y})):Q++}else if(Z&U){if(Q>0)Q--;else if(X>0){for(V=0;!J[J.length-1].o;)J.pop();var fr=J[J.length-1].t;null!=fr&&(b.set(fr,Y),b.set(Y,fr)),J.pop(),X--}K=J[J.length-1],d[Y]=K.v,K.i&&c(Y,K.i)}else Z&T?(0===Q&&(V>0?V--:!K.o&&J.length>1&&(J.pop(),K=J[J.length-1])),d[Y]=K.v):Z&I&&(d[Y]=k.level);else d[Y]=K.v,K.i&&Z!==R&&c(Y,K.i)}for(var ar=[],nr=null,vr=k.start;vr<=k.end;vr++){var ir=f[vr];if(!(ir&o)){var or=d[vr],tr=ir&n,ur=ir===U;nr&&or===nr.v?(nr.u=vr,nr.l=tr):ar.push(nr={k:vr,u:vr,v:or,h:ur,l:tr})}}for(var lr=[],cr=0;cr<ar.length;cr++){var dr=ar[cr];if(!dr.h||dr.h&&!b.has(dr.k)){for(var br=[nr=dr],sr=void 0;nr&&nr.l&&null!=(sr=b.get(nr.u));)for(var kr=cr+1;kr<ar.length;kr++)if(ar[kr].k===sr){br.push(nr=ar[kr]);break}for(var hr=[],mr=0;mr<br.length;mr++)for(var jr=br[mr],gr=jr.k;gr<=jr.u;gr++)hr.push(gr);for(var pr=d[hr[0]],qr=k.level,wr=hr[0]-1;wr>=0;wr--)if(!(f[wr]&o)){qr=d[wr];break}var xr=hr[hr.length-1],_r=d[xr],yr=k.level;if(!(f[xr]&n))for(var Mr=xr+1;Mr<=k.end;Mr++)if(!(f[Mr]&o)){yr=d[Mr];break}lr.push({m:hr,j:Math.max(qr,pr)%2?w:q,g:Math.max(yr,_r)%2?w:q})}}for(var zr=0;zr<lr.length;zr++){var Ir=lr[zr],Lr=Ir.m,Sr=Ir.j,Rr=Ir.g,Or=1&d[Lr[0]]?w:q;if(u.get(O))for(var Er=0;Er<Lr.length;Er++){var Nr=Lr[Er];if(f[Nr]&O){for(var Ar=Sr,Dr=Er-1;Dr>=0;Dr--)if(!(f[Lr[Dr]]&o)){Ar=f[Lr[Dr]];break}c(Nr,Ar&(n|U)?S:Ar)}}if(u.get(x))for(var Pr=0;Pr<Lr.length;Pr++){var Tr=Lr[Pr];if(f[Tr]&x)for(var Wr=Pr-1;Wr>=-1;Wr--){var Br=-1===Wr?Sr:f[Lr[Wr]];if(Br&v){Br===E&&c(Tr,M);break}}}if(u.get(E))for(var Fr=0;Fr<Lr.length;Fr++){var Ur=Lr[Fr];f[Ur]&E&&c(Ur,w)}if(u.get(_)||u.get(z))for(var Cr=1;Cr<Lr.length-1;Cr++){var Gr=Lr[Cr];if(f[Gr]&(_|z)){for(var Hr=0,Jr=0,Kr=Cr-1;Kr>=0&&(Hr=f[Lr[Kr]])&o;Kr--);for(var Qr=Cr+1;Qr<Lr.length&&(Jr=f[Lr[Qr]])&o;Qr++);Hr===Jr&&(f[Gr]===_?Hr===x:Hr&(x|M))&&c(Gr,Hr)}}if(u.get(x))for(var Vr=0;Vr<Lr.length;Vr++){var Xr=Lr[Vr];if(f[Xr]&x){for(var Yr=Vr-1;Yr>=0&&f[Lr[Yr]]&(y|o);Yr--)c(Lr[Yr],x);for(Vr++;Vr<Lr.length&&f[Lr[Vr]]&(y|o|x);Vr++)f[Lr[Vr]]!==x&&c(Lr[Vr],x)}}if(u.get(y)||u.get(_)||u.get(z))for(var Zr=0;Zr<Lr.length;Zr++){var $r=Lr[Zr];if(f[$r]&(y|_|z)){c($r,S);for(var re=Zr-1;re>=0&&f[Lr[re]]&o;re--)c(Lr[re],S);for(var ee=Zr+1;ee<Lr.length&&f[Lr[ee]]&o;ee++)c(Lr[ee],S)}}if(u.get(x))for(var fe=0,ae=Sr;fe<Lr.length;fe++){var ne=Lr[fe],ve=f[ne];ve&x?ae===q&&c(ne,q):ve&v&&(ae=ve)}if(u.get(i)){for(var ie=w|x|M,oe=ie|q,te=[],ue=[],le=0;le<Lr.length;le++)if(f[Lr[le]]&i){var ce=r[Lr[le]],de=void 0;if(null!==j(ce)){if(!(ue.length<63))break;ue.push({char:ce,seqIndex:le})}else if(null!==(de=g(ce)))for(var be=ue.length-1;be>=0;be--){var se=ue[be].char;if(se===de||se===g(p(ce))||j(p(se))===ce){te.push([ue[be].seqIndex,le]),ue.length=be;break}}}te.sort((function(r,e){return r[0]-e[0]}));for(var ke=0;ke<te.length;ke++){for(var he=te[ke],me=he[0],je=he[1],ge=!1,pe=0,qe=me+1;qe<je;qe++){var we=Lr[qe];if(f[we]&oe){ge=!0;var xe=f[we]&ie?w:q;if(xe===Or){pe=xe;break}}}if(ge&&!pe){pe=Sr;for(var _e=me-1;_e>=0;_e--){var ye=Lr[_e];if(f[ye]&oe){var Me=f[ye]&ie?w:q;pe=Me!==Or?Me:Or;break}}}if(pe){if(f[Lr[me]]=f[Lr[je]]=pe,pe!==Or)for(var ze=me+1;ze<Lr.length;ze++)if(!(f[Lr[ze]]&o)){l(r[Lr[ze]])&O&&(f[Lr[ze]]=pe);break}if(pe!==Or)for(var Ie=je+1;Ie<Lr.length;Ie++)if(!(f[Lr[Ie]]&o)){l(r[Lr[Ie]])&O&&(f[Lr[Ie]]=pe);break}}}for(var Le=0;Le<Lr.length;Le++)if(f[Lr[Le]]&i){for(var Se=Le,Re=Le,Oe=Sr,Ee=Le-1;Ee>=0;Ee--){if(!(f[Lr[Ee]]&o)){Oe=f[Lr[Ee]]&ie?w:q;break}Se=Ee}for(var Ne=Rr,Ae=Le+1;Ae<Lr.length;Ae++){if(!(f[Lr[Ae]]&(i|o))){Ne=f[Lr[Ae]]&ie?w:q;break}Re=Ae}for(var De=Se;De<=Re;De++)f[Lr[De]]=Oe===Ne?Oe:Or;Le=Re}}}for(var Pe=k.start;Pe<=k.end;Pe++){var Te=d[Pe],We=f[Pe];if(1&Te?We&(q|x|M)&&d[Pe]++:We&w?d[Pe]++:We&(M|x)&&(d[Pe]+=2),We&o&&(d[Pe]=0===Pe?k.level:d[Pe-1]),Pe===k.end||l(r[Pe])&(L|I))for(var Be=Pe;Be>=0&&l(r[Be])&t;Be--)d[Be]=k.level}}return{levels:d,paragraphs:s};function Fe(e,a){for(var v=e;v<r.length;v++){var i=f[v];if(i&(w|E))return 1;if(i&(I|q)||a&&i===U)return 0;if(i&n){var o=Ue(v);v=-1===o?r.length:o}}return 0}function Ue(e){for(var a=1,v=e+1;v<r.length;v++){var i=f[v];if(i&I)break;if(i&U){if(0==--a)return v}else i&n&&a++}return-1}},r.getMirroredCharacter=G,r.getMirroredCharactersMap=function(r,e,f,a){var n=r.length;f=Math.max(0,null==f?0:+f),a=Math.min(n-1,null==a?n-1:+a);for(var v=new Map,i=f;i<=a;i++)if(1&e[i]){var o=G(r[i]);null!==o&&v.set(i,o)}return v},r.getReorderSegments=H,r.getReorderedIndices=J,r.getReorderedString=function(r,e,f,a){var n=J(r,e,f,a),v=[].concat(r);return n.forEach((function(f,a){v[a]=(1&e.levels[f]?G(r[f]):null)||r[f]})),v.join("")},r.openingToClosingBracket=j,Object.defineProperty(r,"p",{value:!0}),r}({})}export default r;
