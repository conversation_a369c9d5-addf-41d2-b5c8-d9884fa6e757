export function exportToCSV(sensors) {
  const headers = ['Name', 'X', 'Y', 'Z', 'Type', 'Color']
  const rows = sensors.map(sensor => [
    sensor.name,
    sensor.position[0].toFixed(4),
    sensor.position[1].toFixed(4),
    sensor.position[2].toFixed(4),
    sensor.type,
    sensor.color
  ])

  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `auv-sensors-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

export function importFromCSV(csvText) {
  const lines = csvText.trim().split('\n')
  if (lines.length < 2) {
    throw new Error('CSV file must contain at least a header and one data row')
  }

  const headers = lines[0].split(',').map(h => h.trim())
  const sensors = []

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim())
    
    if (values.length < 5) {
      console.warn(`Skipping row ${i + 1}: insufficient data`)
      continue
    }

    try {
      const sensor = {
        name: values[0] || `Sensor ${i}`,
        position: [
          parseFloat(values[1]) || 0,
          parseFloat(values[2]) || 0,
          parseFloat(values[3]) || 0
        ],
        type: values[4] || 'generic',
        color: values[5] || '#ffffff'
      }

      sensors.push(sensor)
    } catch (error) {
      console.warn(`Error parsing row ${i + 1}:`, error)
    }
  }

  return sensors
}
