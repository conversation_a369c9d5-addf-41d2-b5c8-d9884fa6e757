function t(r){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(r)}var r=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],a=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function e(t,r,a,e,n,i){if("string"==typeof t&&(t=document.getElementById(t)),t&&("HTMLImageElement"!==Object.prototype.toString.call(t).slice(8,-1)||"naturalWidth"in t)){var f=n?"offset":"natural",c=t[f+"Width"],l=t[f+"Height"];if("ImageBitmap"===Object.prototype.toString.call(t).slice(8,-1)&&(c=t.width,l=t.height),"string"==typeof r&&(r=document.getElementById(r)),r&&"getContext"in r){i||(r.style.width=c+"px",r.style.height=l+"px"),r.width=c,r.height=l;var s=r.getContext("2d");s.clearRect(0,0,c,l),s.drawImage(t,0,0,t.naturalWidth,t.naturalHeight,0,0,c,l),isNaN(a)||a<1||(e?o(r,0,0,c,l,a):g(r,0,0,c,l,a))}}}function n(r,a,e,n,o){if("string"==typeof r&&(r=document.getElementById(r)),!r||"object"!==t(r)||!("getContext"in r))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var i=r.getContext("2d");try{return i.getImageData(a,e,n,o)}catch(t){throw new Error("unable to access image data: "+t)}}function o(t,r,a,e,o,g){if(!(isNaN(g)||g<1)){g|=0;var f=n(t,r,a,e,o);f=i(f,r,a,e,o,g),t.getContext("2d").putImageData(f,r,a)}}function i(t,e,n,o,i,g){for(var f,l=t.data,s=2*g+1,v=o-1,u=i-1,b=g+1,x=b*(b+1)/2,h=new c,m=h,y=1;y<s;y++)m=m.next=new c,y===b&&(f=m);m.next=h;for(var p=null,d=null,w=0,B=0,C=r[g],I=a[g],E=0;E<i;E++){m=h;for(var S=l[B],N=l[B+1],R=l[B+2],D=l[B+3],G=0;G<b;G++)m.r=S,m.g=N,m.b=R,m.a=D,m=m.next;for(var j=0,A=0,H=0,T=0,W=b*S,O=b*N,L=b*R,M=b*D,k=x*S,q=x*N,z=x*R,F=x*D,J=1;J<b;J++){var K=B+((v<J?v:J)<<2),P=l[K],Q=l[K+1],U=l[K+2],V=l[K+3],X=b-J;k+=(m.r=P)*X,q+=(m.g=Q)*X,z+=(m.b=U)*X,F+=(m.a=V)*X,j+=P,A+=Q,H+=U,T+=V,m=m.next}p=h,d=f;for(var Y=0;Y<o;Y++){var Z=F*C>>>I;if(l[B+3]=Z,0!==Z){var $=255/Z;l[B]=(k*C>>>I)*$,l[B+1]=(q*C>>>I)*$,l[B+2]=(z*C>>>I)*$}else l[B]=l[B+1]=l[B+2]=0;k-=W,q-=O,z-=L,F-=M,W-=p.r,O-=p.g,L-=p.b,M-=p.a;var _=Y+g+1;_=w+(_<v?_:v)<<2,k+=j+=p.r=l[_],q+=A+=p.g=l[_+1],z+=H+=p.b=l[_+2],F+=T+=p.a=l[_+3],p=p.next;var tt=d,rt=tt.r,at=tt.g,et=tt.b,nt=tt.a;W+=rt,O+=at,L+=et,M+=nt,j-=rt,A-=at,H-=et,T-=nt,d=d.next,B+=4}w+=o}for(var ot=0;ot<o;ot++){var it=l[B=ot<<2],gt=l[B+1],ft=l[B+2],ct=l[B+3],lt=b*it,st=b*gt,vt=b*ft,ut=b*ct,bt=x*it,xt=x*gt,ht=x*ft,mt=x*ct;m=h;for(var yt=0;yt<b;yt++)m.r=it,m.g=gt,m.b=ft,m.a=ct,m=m.next;for(var pt=o,dt=0,wt=0,Bt=0,Ct=0,It=1;It<=g;It++){B=pt+ot<<2;var Et=b-It;bt+=(m.r=it=l[B])*Et,xt+=(m.g=gt=l[B+1])*Et,ht+=(m.b=ft=l[B+2])*Et,mt+=(m.a=ct=l[B+3])*Et,Ct+=it,dt+=gt,wt+=ft,Bt+=ct,m=m.next,It<u&&(pt+=o)}B=ot,p=h,d=f;for(var St=0;St<i;St++){var Nt=B<<2;l[Nt+3]=ct=mt*C>>>I,ct>0?(ct=255/ct,l[Nt]=(bt*C>>>I)*ct,l[Nt+1]=(xt*C>>>I)*ct,l[Nt+2]=(ht*C>>>I)*ct):l[Nt]=l[Nt+1]=l[Nt+2]=0,bt-=lt,xt-=st,ht-=vt,mt-=ut,lt-=p.r,st-=p.g,vt-=p.b,ut-=p.a,Nt=ot+((Nt=St+b)<u?Nt:u)*o<<2,bt+=Ct+=p.r=l[Nt],xt+=dt+=p.g=l[Nt+1],ht+=wt+=p.b=l[Nt+2],mt+=Bt+=p.a=l[Nt+3],p=p.next,lt+=it=d.r,st+=gt=d.g,vt+=ft=d.b,ut+=ct=d.a,Ct-=it,dt-=gt,wt-=ft,Bt-=ct,d=d.next,B+=o}}return t}function g(t,r,a,e,o,i){if(!(isNaN(i)||i<1)){i|=0;var g=n(t,r,a,e,o);g=f(g,r,a,e,o,i),t.getContext("2d").putImageData(g,r,a)}}function f(t,e,n,o,i,g){for(var f,l=t.data,s=2*g+1,v=o-1,u=i-1,b=g+1,x=b*(b+1)/2,h=new c,m=h,y=1;y<s;y++)m=m.next=new c,y===b&&(f=m);m.next=h;for(var p,d,w=null,B=null,C=r[g],I=a[g],E=0,S=0,N=0;N<i;N++){var R=l[S],D=l[S+1],G=l[S+2],j=b*R,A=b*D,H=b*G,T=x*R,W=x*D,O=x*G;m=h;for(var L=0;L<b;L++)m.r=R,m.g=D,m.b=G,m=m.next;for(var M=0,k=0,q=0,z=1;z<b;z++)p=S+((v<z?v:z)<<2),T+=(m.r=R=l[p])*(d=b-z),W+=(m.g=D=l[p+1])*d,O+=(m.b=G=l[p+2])*d,M+=R,k+=D,q+=G,m=m.next;w=h,B=f;for(var F=0;F<o;F++)l[S]=T*C>>>I,l[S+1]=W*C>>>I,l[S+2]=O*C>>>I,T-=j,W-=A,O-=H,j-=w.r,A-=w.g,H-=w.b,p=E+((p=F+g+1)<v?p:v)<<2,T+=M+=w.r=l[p],W+=k+=w.g=l[p+1],O+=q+=w.b=l[p+2],w=w.next,j+=R=B.r,A+=D=B.g,H+=G=B.b,M-=R,k-=D,q-=G,B=B.next,S+=4;E+=o}for(var J=0;J<o;J++){var K=l[S=J<<2],P=l[S+1],Q=l[S+2],U=b*K,V=b*P,X=b*Q,Y=x*K,Z=x*P,$=x*Q;m=h;for(var _=0;_<b;_++)m.r=K,m.g=P,m.b=Q,m=m.next;for(var tt=0,rt=0,at=0,et=1,nt=o;et<=g;et++)S=nt+J<<2,Y+=(m.r=K=l[S])*(d=b-et),Z+=(m.g=P=l[S+1])*d,$+=(m.b=Q=l[S+2])*d,tt+=K,rt+=P,at+=Q,m=m.next,et<u&&(nt+=o);S=J,w=h,B=f;for(var ot=0;ot<i;ot++)l[p=S<<2]=Y*C>>>I,l[p+1]=Z*C>>>I,l[p+2]=$*C>>>I,Y-=U,Z-=V,$-=X,U-=w.r,V-=w.g,X-=w.b,p=J+((p=ot+b)<u?p:u)*o<<2,Y+=tt+=w.r=l[p],Z+=rt+=w.g=l[p+1],$+=at+=w.b=l[p+2],w=w.next,U+=K=B.r,V+=P=B.g,X+=Q=B.b,tt-=K,rt-=P,at-=Q,B=B.next,S+=o}return t}var c=function t(){!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null};export{c as BlurStack,g as canvasRGB,o as canvasRGBA,e as image,f as imageDataRGB,i as imageDataRGBA};
//# sourceMappingURL=stackblur-es.min.js.map
