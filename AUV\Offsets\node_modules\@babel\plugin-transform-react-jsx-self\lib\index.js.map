{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "TRACE_ID", "getThisFunctionParent", "path", "scope", "isFunctionParent", "isArrowFunctionExpression", "parent", "isDerivedClass", "classPath", "node", "superClass", "isThisAllowed", "parentMethodOrFunction", "isMethod", "kind", "parentPath", "_default", "exports", "default", "declare", "api", "assertVersion", "visitor", "JSXOpeningElement", "id", "t", "jsxIdentifier", "trace", "thisExpression", "attributes", "push", "jsxAttribute", "jsxExpressionContainer", "name", "Program", "traverse"], "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * This adds a __self={this} JSX attribute to all JSX elements, which <PERSON><PERSON> will use\n * to generate some runtime warnings. However, if the JSX element appears within a\n * constructor of a derived class, `__self={this}` will not be inserted in order to\n * prevent runtime errors.\n *\n * == JSX Literals ==\n *\n * <sometag />\n *\n * becomes:\n *\n * <sometag __self={this} />\n */\nimport { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\nimport type { Visitor, NodePath } from \"@babel/core\";\n\nconst TRACE_ID = \"__self\";\n\n/**\n * Finds the closest parent function that provides `this`. Specifically, this looks for\n * the first parent function that isn't an arrow function.\n *\n * Derived from `Scope#getFunctionParent`\n */\nfunction getThisFunctionParent(\n  path: NodePath<t.JSXOpeningElement>,\n): NodePath<Exclude<t.FunctionParent, t.ArrowFunctionExpression>> | null {\n  let scope = path.scope;\n  do {\n    const { path } = scope;\n    if (path.isFunctionParent() && !path.isArrowFunctionExpression()) {\n      return path;\n    }\n  } while ((scope = scope.parent));\n  return null;\n}\n\n/**\n * Returns whether the class has specified a superclass.\n */\nfunction isDerivedClass(classPath: NodePath<t.Class>) {\n  return classPath.node.superClass !== null;\n}\n\n/**\n * Returns whether `this` is allowed at given path.\n */\nfunction isThisAllowed(path: NodePath<t.JSXOpeningElement>) {\n  // This specifically skips arrow functions as they do not rewrite `this`.\n  const parentMethodOrFunction = getThisFunctionParent(path);\n  if (parentMethodOrFunction === null) {\n    // We are not in a method or function. It is fine to use `this`.\n    return true;\n  }\n  if (!parentMethodOrFunction.isMethod()) {\n    // If the closest parent is a regular function, `this` will be rebound, therefore it is fine to use `this`.\n    return true;\n  }\n  // Current node is within a method, so we need to check if the method is a constructor.\n  if (parentMethodOrFunction.node.kind !== \"constructor\") {\n    // We are not in a constructor, therefore it is always fine to use `this`.\n    return true;\n  }\n  // Now we are in a constructor. If it is a derived class, we do not reference `this`.\n  return !isDerivedClass(\n    parentMethodOrFunction.parentPath.parentPath as NodePath<t.Class>,\n  );\n}\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const visitor: Visitor = {\n    JSXOpeningElement(path) {\n      if (!isThisAllowed(path)) {\n        return;\n      }\n      const node = path.node;\n      const id = t.jsxIdentifier(TRACE_ID);\n      const trace = t.thisExpression();\n\n      node.attributes.push(t.jsxAttribute(id, t.jsxExpressionContainer(trace)));\n    },\n  };\n\n  return {\n    name: \"transform-react-jsx-self\",\n    visitor: {\n      Program(path) {\n        path.traverse(visitor);\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAcA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAGA,MAAME,QAAQ,GAAG,QAAQ;AAQzB,SAASC,qBAAqBA,CAC5BC,IAAmC,EACoC;EACvE,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACtB,GAAG;IACD,MAAM;MAAED;IAAK,CAAC,GAAGC,KAAK;IACtB,IAAID,IAAI,CAACE,gBAAgB,CAAC,CAAC,IAAI,CAACF,IAAI,CAACG,yBAAyB,CAAC,CAAC,EAAE;MAChE,OAAOH,IAAI;IACb;EACF,CAAC,QAASC,KAAK,GAAGA,KAAK,CAACG,MAAM;EAC9B,OAAO,IAAI;AACb;AAKA,SAASC,cAAcA,CAACC,SAA4B,EAAE;EACpD,OAAOA,SAAS,CAACC,IAAI,CAACC,UAAU,KAAK,IAAI;AAC3C;AAKA,SAASC,aAAaA,CAACT,IAAmC,EAAE;EAE1D,MAAMU,sBAAsB,GAAGX,qBAAqB,CAACC,IAAI,CAAC;EAC1D,IAAIU,sBAAsB,KAAK,IAAI,EAAE;IAEnC,OAAO,IAAI;EACb;EACA,IAAI,CAACA,sBAAsB,CAACC,QAAQ,CAAC,CAAC,EAAE;IAEtC,OAAO,IAAI;EACb;EAEA,IAAID,sBAAsB,CAACH,IAAI,CAACK,IAAI,KAAK,aAAa,EAAE;IAEtD,OAAO,IAAI;EACb;EAEA,OAAO,CAACP,cAAc,CACpBK,sBAAsB,CAACG,UAAU,CAACA,UACpC,CAAC;AACH;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,OAAgB,GAAG;IACvBC,iBAAiBA,CAACrB,IAAI,EAAE;MACtB,IAAI,CAACS,aAAa,CAACT,IAAI,CAAC,EAAE;QACxB;MACF;MACA,MAAMO,IAAI,GAAGP,IAAI,CAACO,IAAI;MACtB,MAAMe,EAAE,GAAGC,WAAC,CAACC,aAAa,CAAC1B,QAAQ,CAAC;MACpC,MAAM2B,KAAK,GAAGF,WAAC,CAACG,cAAc,CAAC,CAAC;MAEhCnB,IAAI,CAACoB,UAAU,CAACC,IAAI,CAACL,WAAC,CAACM,YAAY,CAACP,EAAE,EAAEC,WAAC,CAACO,sBAAsB,CAACL,KAAK,CAAC,CAAC,CAAC;IAC3E;EACF,CAAC;EAED,OAAO;IACLM,IAAI,EAAE,0BAA0B;IAChCX,OAAO,EAAE;MACPY,OAAOA,CAAChC,IAAI,EAAE;QACZA,IAAI,CAACiC,QAAQ,CAACb,OAAO,CAAC;MACxB;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}