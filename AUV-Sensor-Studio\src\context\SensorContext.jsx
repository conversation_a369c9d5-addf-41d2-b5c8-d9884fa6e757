import React, { createContext, useContext, useReducer } from 'react'

const SensorContext = createContext()

// Initial state with sample sensors
const initialState = {
  sensors: [
    {
      id: '1',
      name: 'INS Reference',
      position: [0, 0, 0],
      type: 'navigation',
      color: '#ff4444'
    },
    {
      id: '2',
      name: 'Multibeam Sonar',
      position: [0, 0.5, -0.2],
      type: 'sonar',
      color: '#4444ff'
    }
  ],
  selectedSensor: null,
  modelLoaded: false,
  showLabels: true,
  customModelUrl: null,
  modelType: 'fallback', // 'fallback', 'step', 'gltf', 'step-parsed'
  selectedVehicle: 'hugin3000' // 'hugin3000', 'hugin1000'
}

// Reducer for sensor state management
function sensorReducer(state, action) {
  switch (action.type) {
    case 'ADD_SENSOR':
      return {
        ...state,
        sensors: [...state.sensors, action.payload]
      }
    
    case 'UPDATE_SENSOR':
      return {
        ...state,
        sensors: state.sensors.map(sensor =>
          sensor.id === action.payload.id ? { ...sensor, ...action.payload } : sensor
        )
      }
    
    case 'DELETE_SENSOR':
      return {
        ...state,
        sensors: state.sensors.filter(sensor => sensor.id !== action.payload),
        selectedSensor: state.selectedSensor === action.payload ? null : state.selectedSensor
      }
    
    case 'SELECT_SENSOR':
      return {
        ...state,
        selectedSensor: action.payload
      }
    
    case 'SET_MODEL_LOADED':
      return {
        ...state,
        modelLoaded: action.payload
      }

    case 'TOGGLE_LABELS':
      return {
        ...state,
        showLabels: !state.showLabels
      }

    case 'SET_CUSTOM_MODEL':
      return {
        ...state,
        customModelUrl: action.payload.url,
        modelType: action.payload.type,
        modelLoaded: false
      }

    case 'SET_VEHICLE':
      return {
        ...state,
        selectedVehicle: action.payload,
        // Reset to fallback model when changing vehicles
        customModelUrl: null,
        modelType: 'fallback',
        modelLoaded: false
      }

    default:
      return state
  }
}

export function SensorProvider({ children }) {
  const [state, dispatch] = useReducer(sensorReducer, initialState)

  const addSensor = (sensor) => {
    const newSensor = {
      ...sensor,
      id: Date.now().toString(),
      color: sensor.color || `#${Math.floor(Math.random()*16777215).toString(16)}`
    }
    dispatch({ type: 'ADD_SENSOR', payload: newSensor })
  }

  const updateSensor = (id, updates) => {
    dispatch({ type: 'UPDATE_SENSOR', payload: { id, ...updates } })
  }

  const deleteSensor = (id) => {
    dispatch({ type: 'DELETE_SENSOR', payload: id })
  }

  const selectSensor = (id) => {
    dispatch({ type: 'SELECT_SENSOR', payload: id })
  }

  const setModelLoaded = (loaded) => {
    dispatch({ type: 'SET_MODEL_LOADED', payload: loaded })
  }

  const toggleLabels = () => {
    dispatch({ type: 'TOGGLE_LABELS' })
  }

  const setCustomModel = (url, type) => {
    dispatch({ type: 'SET_CUSTOM_MODEL', payload: { url, type } })
  }

  const setVehicle = (vehicle) => {
    dispatch({ type: 'SET_VEHICLE', payload: vehicle })
  }

  const value = {
    ...state,
    addSensor,
    updateSensor,
    deleteSensor,
    selectSensor,
    setModelLoaded,
    toggleLabels,
    setCustomModel,
    setVehicle
  }

  return (
    <SensorContext.Provider value={value}>
      {children}
    </SensorContext.Provider>
  )
}

export function useSensors() {
  const context = useContext(SensorContext)
  if (!context) {
    throw new Error('useSensors must be used within a SensorProvider')
  }
  return context
}
